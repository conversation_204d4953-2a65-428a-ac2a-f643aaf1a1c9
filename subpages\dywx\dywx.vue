<template>
	<view style="height: 100vh;">
		<image :src="img" mode="widthFix" show-menu-by-longpress="true"></image>
	</view>
</template>

<script>
import { getPhoneImage } from "../../Api/index.js"
export default {
	data() {
		return {
			img: ""
		};
	},
	async onLoad(options) {
		if (options.phone) {
			let { data } = await getPhoneImage(options.phone)
			if (data.data.length > 0) {
				this.img = data.data[0].img
			} else {
				uni.showToast({
					title: "失败，请联系客服",
					icon: "error"
				})
			}
		}

	},
	async mounted() {


		// uni.previewImage({
		// 	current:0,
		// 	urls:['https://cdn.lshifu.top/dywx/0c5c1410bce90e1b86b04b03b8c50c9.jpg']
		// })
	}
}
</script>

<style lang="scss" scoped>
image {
	position: fixed;
	top: 50%;
	left: 50%;
	width: 100vw;
	transform: translate(-50%, -50%);
}
</style>
