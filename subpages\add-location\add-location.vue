<template>
	<view class="container">
		<!-- 顶部导航栏 -->
		<view class="nav-bar">
			<view class="status-bar"></view>
			<view class="header">
				<view class="back-btn" @tap="goBack">
					<view class="back-icon">
						<view class="back-arrow"></view>
					</view>
				</view>
				<text class="title">{{ isEdit ? '编辑场地' : '提交场地信息' }}</text>
			</view>
		</view>

		<!-- 页面内容区域 -->
		<view class="page-content">
			<!-- 隐私提示 -->
			<view class="privacy-notice">
				<text class="privacy-text">注意：提交信息仅在场地卡片中展示，不做其他用途。</text>
			</view>
			<!-- 预览卡片 -->
			<view class="preview-card">
				<view class="preview-header">
					<text class="preview-title">预览效果</text>
				</view>
				<view class="preview-content">
					<view class="preview-image-wrapper">
						<image v-if="formData.images.length > 0" :src="formData.images[0]" mode="aspectFill"
							class="preview-image" />
						<view v-else class="preview-image-placeholder">
							<text class="iconfont icon-camera"></text>
							<text class="placeholder-text">图片区域</text>
						</view>
					</view>
					<view class="preview-info">
						<text class="preview-name">{{ formData.name || '场地名称' }}</text>
						<text class="preview-type" v-if="false">{{ formData.type || '运动类型' }}</text>
						<text class="preview-address" v-if="false">{{ formData.address || '场地地址' }}</text>
						<text class="preview-desc">{{ formData.description || '场地描述' }}</text>
						<view class="preview-meta">
							<text class="distance">距离：100m</text>
							<button class="navigate-btn">
								<text class="iconfont icon-navigation"></text>
								导航
							</button>
						</view>
					</view>
				</view>
			</view>

			<form @submit="submitForm">
				<!-- 场地名称 -->
				<view class="form-item">
					<text class="label">
						<text class="required">*</text>
						场地名称
						<text class="required-text">（必填）</text>
					</text>
					<input type="text" v-model="formData.name" placeholder="请输入场地名称" class="input" />
				</view>

				<!-- 运动类型 -->
				<view class="form-item">
					<text class="label">
						<text class="required">*</text>
						运动类型
						<text class="required-text">（必填）</text>
					</text>
					<view class="sport-types">
						<view v-for="sport in sports" :key="sport"
							:class="['sport-type', Number(formData.type) == Number(sport.id) ? 'active' : '']"
							@tap="selectSport(sport)">

							{{ sport.sportsName }}
						</view>
					</view>
				</view>

				<!-- 场地地址 -->
				<view class="form-item">
					<text class="label">
						<text class="required">*</text>
						场地地址
						<text class="required-text">（必填）</text>
					</text>
					<view class="address-input" @tap="chooseLocation">
						<text v-if="formData.address" class="address-text">{{ formData.address }}</text>
						<text v-else class="placeholder">点击选择地址</text>
						<text class="iconfont icon-location"></text>
					</view>
				</view>

				<!-- 联系电话 -->
				<view class="form-item" >
					<text class="label">
						联系电话
						<text class="optional-text">（选填）</text>
					</text>
					<input type="number" v-model="formData.phone" placeholder="请输入联系电话" class="input" maxlength="11" />
					<text class="privacy-notice">联系方式仅在详情页面进行展示，不做其他用途。</text>
				</view>

				<!-- 场地描述 -->
				<view class="form-item">
					<text class="label">
						<text class="required">*</text>
						场地描述
						<text class="required-text">（必填）</text>
					</text>
					<textarea v-model="formData.description" placeholder="例如：自带球拍，两个网子，停车好停，灰大自己带毛巾，地胶好等" class="textarea" />
				</view>

				<!-- 资费价格设置 -->
				<view class="form-item">
					<text class="label">
						<text class="required">*</text>
						资费价格
						<text class="required-text">（必填）</text>
					</text>
					<view class="fee-options">
						<view v-for="fee in feeOptions" :key="fee.value"
							:class="['fee-option', formData.feeType === fee.value ? 'active' : '']" @tap="selectFeeType(fee.value)">
							{{ fee.label }}
						</view>
					</view>

					<!-- 每小时价格输入 -->
					<view class="price-input-wrapper" v-if="formData.feeType === 'hourly'">
						<input type="digit" v-model="formData.hourlyPrice" placeholder="请输入每小时价格" class="price-input" />
						<text class="price-unit">元/小时</text>
					</view>

					<!-- 每次价格输入 -->
					<view class="price-input-wrapper" v-if="formData.feeType === 'perTime'">
						<input type="digit" v-model="formData.perTimePrice" placeholder="请输入每次价格" class="price-input" />
						<text class="price-unit">元/次</text>
					</view>
				</view>

				<!-- 图片上传 -->
				<view class="form-item">
					<text class="label">
						<text class="required">*</text>
						场地图片
						<text class="required-text">（必填）</text>
					</text>
					<view class="image-upload">
						<view class="image-item" v-for="(image, index) in formData.images" :key="index">
							<image :src="image" mode="aspectFill" />
							<text class="delete-btn" @tap="deleteImage(index)" v-if="!isEdit">×</text>
						</view>
						<view class="upload-btn" @tap="chooseImage" v-if="formData.images.length < 6 && !isEdit">
							<text class="iconfont icon-camera"></text>
							<text class="upload-text">上传图片</text>
						</view>
					</view>
				</view>



				<!-- 提交按钮 -->
				<button type="primary" class="submit-btn" :disabled="!isFormValid" @tap="submitForm">
					{{ isEdit ? '保存修改' : '提交场地' }}
				</button>
			</form>
		</view>
		<canvas style="width: 100%; height: 100%; position: fixed; top: -9999px; left: -9999px;" canvas-id="compressCanvas"
			id="compressCanvas"></canvas>
	</view>
</template>

<script>
import {
	compressImage,
	getBase64
} from "@/utools/index.js"
import { addLocation } from "@/Api/index.js"
import Mixins from "../../minix/index.js"
export default {
	  mixins:[Mixins],
	data() {
		return {
			isEdit: false,
			locationId: null,
			sports: [],
			feeOptions: [{
				label: '免费',
				value: 'free'
			},
			{
				label: '未知',
				value: 'unknown'
			},
			{
				label: '每小时',
				value: 'hourly'
			},
			{
				label: '每次',
				value: 'perTime'
			}
			],
			formData: {
				name: '',
				type: '',
				typeName: "",
				address: '',
				description: '',
				images: [],
				latitude: '',
				longitude: '',
				feeType: '',
				hourlyPrice: '',
				perTimePrice: '',
				phone: ''
			},

		}
	},
	watch: {
		'formData.feeType': {
			handler(newval) {
				if (!this.isEdit) {
					this.formData.hourlyPrice = ''
					this.formData.perTimePrice = ''
				}
			}
		},
		'$store.state.sportsWord': {
			handler(n) {
				this.sports = [...n]
			},
			deep: true,
			immediate: true
		}
	},
	onLoad(options) {
		// 通过eventChannel获取数据
		const eventChannel = this.getOpenerEventChannel()
		eventChannel.on('acceptLocationData', (data) => {
			if (data && data.isEdit && data.location) {
				this.isEdit = true

				// 处理图片数据
				const locationData = data.location
				if (typeof locationData.images === 'string') {
					try {
						locationData.images = JSON.parse(locationData.images)
					} catch (e) {
						locationData.images = []
					}
				}

				// 更新表单数据
				this.formData = {
					...this.formData,
					...locationData
				}
			}
		})
	},
	mounted() {


	},
	computed: {
		isFormValid() {
			const basicInfoValid = this.formData.name &&
				this.formData.type &&
				this.formData.address &&
				this.formData.description &&
				this.formData.images.length > 0

			// 检查资费价格是否有效
			let feeValid = this.formData.feeType !== ''

			if (this.formData.feeType === 'hourly') {
				feeValid = feeValid && this.formData.hourlyPrice !== ''
			} else if (this.formData.feeType === 'perTime') {
				feeValid = feeValid && this.formData.perTimePrice !== ''
			}

			return basicInfoValid && feeValid
		}
	},
	methods: {

		goBack() {
			uni.navigateBack()
		},

		// 选择运动类型
		selectSport(sport) {
			if( this.isEdit) return false
			this.formData.type = sport.id
			this.formData.typeName = sport.sportsName
		},

		// 选择地址
		chooseLocation() {
			if (this.isEdit) {
				return false
			}
			uni.chooseLocation({
				success: (res) => {
					this.formData.address = res.address + res.name
					this.formData.latitude = res.latitude
					this.formData.longitude = res.longitude
				}
			})
		},

		// 选择图片
		chooseImage() {
			uni.chooseImage({
				count: 6 - this.formData.images.length,
				success: async (res) => {
					const base64Images = await Promise.all(
						res.tempFilePaths.map(async (item) => {
							const compressedBlob = await compressImage(item);
							const base64 = await getBase64(compressedBlob);
							// uni.setClipboardData({
							// 	data: base64,
							// 	success: (result) => {},
							// 	fail: (error) => {}
							// })
							return base64
						})

					)
					console.log(base64Images);


					this.formData.images = [...this.formData.images, ...base64Images]



				}
			})
			// 压缩图片（质量80%，最大宽度800px）
			// const base64Images = await Promise.all(
			// 	res.tempFilePaths.map(async (item) => {
			// 		const compressedBlob = await compressImage(item, {
			// 			quality: 0.8,
			// 		});
			// 		const base64 = await tempFilePathToBase64(compressedBlob);
			// 		return base64
			// 	})
			// );
			// console.log(base64Images)
			// this.formData.images = [...this.formData.images, ...base64Images]
		},

		// 删除图片
		deleteImage(index) {
			this.formData.images.splice(index, 1)
		},

		// 选择资费类型
		selectFeeType(feeType) {
			this.formData.feeType = feeType
		},

		// 提交表单
		submitForm() {


			if (!this.isFormValid) {
				uni.showToast({
					title: '请填写完整信息',
					icon: 'none'
				})
				return
			}

			// 检查上传图片数量
			if (this.formData.images.length < 4&&!this.isEdit) {
				uni.showModal({
					title: '提示',
					content: '为了使球友更好的了解场地，建议上传图片4张以上',
					cancelText: '继续上传',
					confirmText: '仍要提交',
					success: (res) => {
						if (res.confirm) {
							// 用户选择"仍要提交"
							this.submitData()
						}
						// 用户选择"继续上传"，不做处理，返回页面继续上传
					}
				})
			} else {
				// 图片数量足够，直接提交
				this.submitData()
			}
		},

		// 实际提交数据的方法
		async submitData() {
			let that = this
			// 这里添加提交逻辑
			uni.showLoading({
				title: this.isEdit ? '更新中...' : '提交中...'
			})

			// 如果电话为空，设置为"未知"
			if (this.formData.phone.trim().length === 0) {
				this.formData.phone = "未知"
			}
			await that.$verify()
			addLocation(this.formData).then(res => {
				if (res.data.code !== 200) return;
				uni.showToast({
					title: this.isEdit?"更新成功，待审核中。":"提交成功，待审核中。",
					icon: "none",
					complete() {
						setTimeout(() => {
						that.$verify()

							// 标记首页数据需要刷新
							getApp().globalData = getApp().globalData || {};
							getApp().globalData.needRefreshHomeData = true;
							getApp().globalData.needRefreshNearbyData = true;
							let pages = getCurrentPages();
							let beforePage = pages[pages.length - 2];
							uni.navigateBack({
								delta: 1,
								success: function () {
									// 更新上一页的数据
									if (beforePage && beforePage.$vm) {
										if (beforePage.$vm.getMyLocations) {
											beforePage.$vm.getMyLocations();
										}
									}
								}
							});
						}, 1000)
					}
				})
			})
		}
	}
}
</script>

<style lang="scss">
.container {
	min-height: 100vh;
	background-color: #f5f5f5;
}

.nav-bar {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	z-index: 999;
	background: #4CAF50;
}

.header {
	padding: 20rpx 30rpx;
	display: flex;
	align-items: center;
	position: relative;

	.back-btn {
		position: absolute;
		left: 30rpx;
		color: #fff;
		display: flex;
		align-items: center;
		justify-content: center;
		width: 48rpx;
		height: 48rpx;

		.back-icon {
			width: 36rpx;
			height: 36rpx;
			position: relative;

			.back-arrow {
				width: 20rpx;
				height: 20rpx;
				border-left: 4rpx solid #fff;
				border-bottom: 4rpx solid #fff;
				transform: rotate(45deg);
				position: absolute;
				left: 8rpx;
				top: 8rpx;
			}
		}
	}

	.title {
		flex: 1;
		text-align: center;
		color: #fff;
		font-size: 36rpx;
		font-weight: 500;
	}
}

.page-content {
	padding-top: calc(var(--status-bar-height) + 88rpx);
	padding-bottom: 20rpx;
}

.preview-card {
	margin: 30rpx;
	background: #fff;
	border-radius: 24rpx;
	box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
	overflow: hidden;

	.preview-header {
		padding: 20rpx;
		border-bottom: 2rpx solid #f5f5f5;

		.preview-title {
			font-size: 28rpx;
			color: #666;
		}
	}

	.preview-content {
		.preview-image-wrapper {
			width: 100%;
			height: 400rpx;
			overflow: hidden;
			border-radius: 24rpx 24rpx 0 0;
			position: relative;

			.preview-image {
				width: 100%;
				height: 100%;
				object-fit: cover;
			}

			.preview-image-placeholder {
				width: 100%;
				height: 100%;
				background: #f5f5f5;
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: center;

				.iconfont {
					font-size: 60rpx;
					color: #999;
					margin-bottom: 10rpx;
				}

				.placeholder-text {
					font-size: 26rpx;
					color: #999;
				}
			}
		}

		.preview-info {
			padding: 30rpx;

			.preview-name {
				font-size: 32rpx;
				font-weight: 500;
				margin-bottom: 16rpx;
				display: block;
			}

			.preview-type {
				font-size: 28rpx;
				color: #666;
				margin-bottom: 16rpx;
				display: block;
			}

			.preview-address {
				font-size: 28rpx;
				color: #888;
				margin-bottom: 16rpx;
				display: block;
			}

			.preview-desc {
				font-size: 28rpx;
				color: #666;
				margin: 20rpx 0;
				line-height: 1.5;
			}

			.preview-meta {
				display: flex;
				justify-content: space-between;
				align-items: center;
				margin-top: 16rpx;

				.distance {
					font-size: 28rpx;
					color: #888;
				}

				.navigate-btn {
					margin: 0;
					background: #4CAF50;
					color: #fff;
					// padding: 16rpx 32rpx;
					// border-radius: 40rpx;
					font-size: 28rpx;
					display: flex;
					align-items: center;

					.iconfont {
						margin-right: 8rpx;
					}
				}
			}
		}
	}
}

.form-item {
	background: #fff;
	padding: 30rpx;
	margin: 0 30rpx 20rpx;
	border-radius: 12rpx;
	box-sizing: border-box;
	width: 690rpx;

	.privacy-notice {
		font-size: 24rpx;
		color: #ff4d4f;
		margin-top: 10rpx;
		display: block;
	}

	.label {
		font-size: 28rpx;
		color: #333;
		margin-bottom: 20rpx;
		display: block;

		.required {
			color: #ff4d4f;
			margin-right: 4rpx;
		}

		.required-text {
			color: #ff4d4f;
			font-size: 24rpx;
			margin-left: 8rpx;
		}

		.optional-text {
			color: #999;
			font-size: 24rpx;
			margin-left: 8rpx;
		}
	}

	.input {
		width: 100%;
		height: 80rpx;
		border: 2rpx solid #eee;
		border-radius: 8rpx;
		padding: 0 20rpx;
		font-size: 28rpx;
		box-sizing: border-box;
	}

	.sport-types {
		display: flex;
		flex-wrap: wrap;
		gap: 20rpx;
		width: 100%;

		.sport-type {
			padding: 10rpx 30rpx;
			background: #f5f5f5;
			border-radius: 30rpx;
			font-size: 26rpx;
			color: #666;

			&.active {
				background: #4CAF50;
				color: #fff;
			}
		}
	}

	.address-input {
		display: flex;
		align-items: center;
		justify-content: space-between;
		// height: 80rpx;
		border: 2rpx solid #eee;
		border-radius: 8rpx;
		padding: 20rpx 20rpx;
		box-sizing: border-box;
		width: 100%;

		.address-text {
			font-size: 28rpx;
			color: #333;
			flex: 1;
			// overflow: hidden;
			// text-overflow: ellipsis;
			// white-space: nowrap;
		}

		.placeholder {
			font-size: 28rpx;
			color: #999;
			flex: 1;
		}

		.iconfont {
			font-size: 40rpx;
			color: #666;
			margin-left: 20rpx;
		}
	}

	.textarea {
		width: 100%;
		height: 200rpx;
		border: 2rpx solid #eee;
		border-radius: 8rpx;
		padding: 20rpx;
		font-size: 28rpx;
		box-sizing: border-box;
	}

	.fee-options {
		display: flex;
		flex-wrap: wrap;
		gap: 20rpx;
		width: 100%;
		margin-bottom: 20rpx;

		.fee-option {
			padding: 10rpx 30rpx;
			background: #f5f5f5;
			border-radius: 30rpx;
			font-size: 26rpx;
			color: #666;

			&.active {
				background: #4CAF50;
				color: #fff;
			}
		}
	}

	.price-input-wrapper {
		display: flex;
		align-items: center;
		margin-top: 20rpx;

		.price-input {
			flex: 1;
			height: 80rpx;
			border: 2rpx solid #eee;
			border-radius: 8rpx;
			padding: 0 20rpx;
			font-size: 28rpx;
			margin-right: 20rpx;
		}

		.price-unit {
			font-size: 28rpx;
			color: #666;
			white-space: nowrap;
		}
	}

	.image-upload {
		display: flex;
		flex-wrap: wrap;
		gap: 20rpx;
		width: 100%;

		.image-item {
			width: 200rpx;
			height: 200rpx;
			position: relative;

			image {
				width: 100%;
				height: 100%;
				border-radius: 8rpx;
			}

			.delete-btn {
				position: absolute;
				top: -20rpx;
				right: -20rpx;
				width: 40rpx;
				height: 40rpx;
				background: rgba(0, 0, 0, 0.5);
				color: #fff;
				border-radius: 50%;
				display: flex;
				align-items: center;
				justify-content: center;
				font-size: 32rpx;
			}
		}

		.upload-btn {
			width: 200rpx;
			height: 200rpx;
			background: #f5f5f5;
			border-radius: 8rpx;
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;

			.iconfont {
				font-size: 60rpx;
				color: #999;
				margin-bottom: 10rpx;
			}

			.upload-text {
				font-size: 24rpx;
				color: #999;
			}
		}
	}
}

.privacy-notice {
	padding: 0 30rpx;
	margin-top: 20rpx;

	.privacy-text {
		color: #ff4d4f;
		font-size: 28rpx;
		text-align: center;
		display: block;
	}
}

.submit-btn {
	width: 690rpx;
	height: 88rpx;
	background: #4CAF50;
	color: #fff;
	font-size: 32rpx;
	border-radius: 44rpx;
	margin: 30rpx 30rpx 60rpx;

	&[disabled] {
		background: #ccc;
	}
}
</style>