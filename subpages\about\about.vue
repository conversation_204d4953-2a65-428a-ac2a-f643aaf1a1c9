<template>
	<view class="container">
		<!-- 页面头部 -->
		<view class="nav-bar">
			<view class="status-bar"></view>
			<view class="header">
				<view class="back-btn" @tap="goBack">
					<image class="back-icon" src="/static/icons/arrow-left.svg" mode="aspectFit"></image>
				</view>
				<text class="title">关于我们</text>
				<view class="placeholder"></view>
			</view>
		</view>

		<!-- 页面内容区域 -->
		<view class="page-content">
			<view class="about-content">
				<view class="logo">
					<image src="/static/logo.png" mode="aspectFit"></image>
				</view>
				<view class="app-name" @tap="handleNameClick">今天去哪运动</view>
				<!-- <view class="app-version">版本 1.0.0</view> -->
				
				<view class="about-section">
					<view class="section-title">应用介绍</view>
					<view class="section-content">
						<text>今天去哪运动是一款帮助用户发现和分享运动场地的应用，致力于让每个人都能轻松找到适合的运动场所。</text>
					</view>
				</view>
				
				<view class="about-section">
					<view class="section-title">联系我们</view>
					<view class="section-content">
						<text>邮箱：<EMAIL></text>
						<text>腾讯：QQ1143220150</text>
						<text>官网：https://gjx.xliangxi.vip</text>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				clickCount: 0
			}
		},
		methods: {
			// 返回上一页
			goBack() {
				uni.navigateBack();
			},
			// 处理app-name点击
			handleNameClick() {
				this.clickCount++;
				if (this.clickCount >= 10) {
					// 重置点击次数
					this.clickCount = 0;
					
					// 获取用户ID
					const userInfo = this.$store.state.userinfo;
					if (userInfo && userInfo.id === 1) {
						// 跳转到审核页面
					 return	uni.navigateTo({
							url: '/subpages/examine/examine'
						});
					}
				}
				if(this.clickCount == 3){
					uni.showToast({
						title: '等待一下，马上就到了',
						icon: 'none'
					});
				}
				if(this.clickCount >= 5){
					uni.showToast({
						title: '已经点击了'+this.clickCount+'次了',
						icon: 'none'
					});
				}
				
			}
		}
	}
</script>

<style lang="scss">
	.container {
		min-height: 100vh;
		background-color: #f5f5f5;
	}

	.nav-bar {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		z-index: 999;
		background: #4CAF50;
	}
	
	.status-bar {
		height: var(--status-bar-height);
	}
	
	.header {
		display: flex;
		align-items: center;
		height: 90rpx;
		padding: 0 30rpx;
		
		.back-btn {
			width: 60rpx;
			height: 60rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			
			.back-icon {
				width: 40rpx;
				height: 40rpx;
				filter: brightness(10);
			}
		}
		
		.title {
			flex: 1;
			text-align: center;
			font-size: 36rpx;
			color: #fff;
			font-weight: 500;
		}
		
		.placeholder {
			width: 60rpx;
		}
	}

	.page-content {
		padding-top: calc(var(--status-bar-height) + 90rpx);
		padding-bottom: 30rpx;
	}
	
	.about-content {
		padding: 30rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
		
		.logo {
			width: 180rpx;
			height: 180rpx;
			margin: 50rpx 0 30rpx;
			
			image {
				width: 100%;
				height: 100%;
			}
		}
		
		.app-name {
			font-size: 40rpx;
			font-weight: bold;
			color: #333;
			margin-bottom: 10rpx;
		}
		
		.app-version {
			font-size: 28rpx;
			color: #999;
			margin-bottom: 60rpx;
		}
	}
	
	.about-section {
		width: 100%;
		background: #fff;
		border-radius: 16rpx;
		padding: 30rpx;
		margin-bottom: 30rpx;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
		
		.section-title {
			font-size: 32rpx;
			font-weight: 500;
			color: #333;
			margin-bottom: 20rpx;
			position: relative;
			padding-left: 20rpx;
			
			&:before {
				content: '';
				position: absolute;
				left: 0;
				top: 50%;
				transform: translateY(-50%);
				width: 8rpx;
				height: 30rpx;
				background: #4CAF50;
				border-radius: 4rpx;
			}
		}
		
		.section-content {
			font-size: 28rpx;
			color: #666;
			line-height: 1.8;
			
			text {
				display: block;
				margin-bottom: 10rpx;
			}
		}
	}
</style> 