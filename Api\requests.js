import { $http } from '@escook/request-miniprogram'

uni.$http = $http
// 配置请求根路径
$http.baseUrl = 'https://api.xliangxi.vip:3000'
// $http.baseUrl = 'http://************:3000'
// $http.baseUrl = 'http://*************:3000'

// 请求开始之前做一些事情
$http.beforeRequest = function (options) {
  // uni.showLoading({
  //   title: '数据加载中...',
  // })
	options.header = {
		"Authorization": uni.getStorageSync("ydToken"),
	}
}

// 请求完成之后做一些事情
$http.afterRequest = function (res) {
  if (res.data.code != 200) {
    uni.showToast({
      title: res.data.msg,
      icon: 'error'
    });
    // 抛出错误，让 Promise 进入 reject
    throw new Error(res.data.msg || '请求失败');
  }
  uni.hideLoading();
}

export default $http