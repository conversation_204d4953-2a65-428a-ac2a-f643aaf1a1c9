<template>
	<view class="container">
		<!-- 分享诱导弹窗 -->
		<view class="share-modal" v-if="showShareModal" @tap="closeShareModal">
			<view class="share-content" @tap.stop>
				<view class="share-header">
					<image src="../../static/logo.png" class="share-logo" mode="aspectFit"></image>
					<view class="share-title">发现好用的运动场地？</view>
					<view class="share-subtitle">分享给朋友一起运动吧！</view>
				</view>

				<view class="share-benefits">
					<view class="benefit-item">
						<text class="benefit-icon">🏃‍♂️</text>
						<text class="benefit-text">找到更多运动伙伴</text>
					</view>
					<view class="benefit-item">
						<text class="benefit-icon">📍</text>
						<text class="benefit-text">发现附近优质场地</text>
					</view>
					<view class="benefit-item">
						<text class="benefit-icon">💪</text>
						<text class="benefit-text">一起坚持运动习惯</text>
					</view>
				</view>

				<view class="share-actions">
					<button class="share-btn primary" open-type="share" @tap="shareToFriend">
						<text class="share-btn-icon">📤</text>
						分享给朋友
					</button>
					<button class="share-btn secondary" @tap="addToMiniProgram">
						<text class="share-btn-icon">⭐</text>
						添加到我的小程序
					</button>
				</view>

				<view class="share-close" @tap="closeShareModal">
					<text class="close-text">暂不分享</text>
				</view>
			</view>
		</view>
		<!-- 顶部导航栏 -->
		<view class="nav-bar">
			<view class="status-bar"></view>
			<view class="header">
				<text class="title">今天去哪运动</text>
			</view>
		</view>

		<!-- 页面内容区域 -->
		<view class="page-content">
			<!-- 筛选条 -->
			<view class="filter-bar">
				<picker :range="distanceOptions" @change="onDistanceChange">
					<view class="filter-item">
						距离排序：{{ distanceOptions[formData.selectedDistanceIndex] }}
					</view>
				</picker>
				<picker :range="typeOptions" @change="onTypeChange">
					<view class="filter-item">
						类型：{{ typeOptions[formData.selectedTypeIndex] }}
					</view>
				</picker>
			</view>

			<!-- 运动类型标签 -->
			<scroll-view class="sports-tabs" scroll-x="true" show-scrollbar="false">
				<view v-for="(sport, index) in selectedSports" :key="index" class="sport-tab"
					:class="{ active: currentSport.id === sport.id }" @tap="selectSport(sport)">
					{{ sport.sportsName }}
				</view>
				<view class="add-sport-btn" @tap="sportModal">
					<text class="plus">+</text>
				</view>
			</scroll-view>

			<!-- 地点列表 -->
			<scroll-view :scroll-top="scrollTop" class="locations" scroll-y="true"
				lower-threshold="50" @scrolltolower="loadMore"
				refresher-enabled="true" :refresher-triggered="refreshTriggered" @refresherrefresh="onRefresh"
				enable-back-to-top="true"
				scroll-with-animation="false">
				<!-- 骨架屏 - 减少数量，提高渲染速度 -->
				<view v-if="loading && locations.length === 0" class="skeleton">
					<view v-for="i in 2" :key="i" class="skeleton-card">
						<view class="skeleton-image"></view>
						<view class="skeleton-info">
							<view class="skeleton-title"></view>
							<view class="skeleton-line"></view>
							<view class="skeleton-footer">
								<view class="skeleton-distance"></view>
								<view class="skeleton-btn"></view>
							</view>
						</view>
					</view>
				</view>

				<!-- 实际内容 -->
				<view v-else v-for="(location, index) in locations" :key="index" class="location-card"
					@click="lookDetail(location)">
					<swiper class="location-image" :indicator-dots="location.images && location.images.length > 1"
						:autoplay="false" circular>
						<swiper-item v-for="(img, imgIndex) in location.images" :key="imgIndex">
							<image :src="img" mode="aspectFill" class="location-image-item"
								lazy-load="true"
								:fade-show="true"
								loading="lazy"
								@error="handleImageError"
								@load="handleImageLoad" />
						</swiper-item>
						<swiper-item v-if="!location.images || location.images.length === 0">
							<image src="/static/logo.png" mode="aspectFill" class="location-image-item" />
						</swiper-item>
					</swiper>
					<view class="location-info">
						<view class="location-title">
							<text class="location-name">{{ location.name }}</text>
							<text v-if="location.feeType == 'free'" class="price free">免费</text>
							<text v-else-if="location.feeType == 'unknown'" class="price free">未知</text>
							<text v-else-if="location.feeType == 'hourly'" class="price">¥{{ location.hourlyPrice }}/小时</text>
							<text v-else-if="location.feeType == 'perTime'" class="price">¥{{ location.perTimePrice }}/次</text>
						</view>
						<view class="location-desc">{{ location.description }}</view>
						<view class="location-desc">{{ location.address }}</view>
						<view class="location-footer">
							<text class="distance">{{ getDistanceMeters(location.distance_meters) }}</text>
							<button class="navigate-btn" @tap.stop="showMapOptions(location)">
								<text class="iconfont icon-navigation"></text>
								导航
							</button>
						</view>
					</view>
				</view>

				<!-- 加载状态提示 -->
				<view class="loading-status">
					<text v-if="loading && locations.length > 0">正在加载中...</text>
					<text v-else-if="!hasMore && locations.length > 0">没有更多数据了</text>
					<!-- <text v-else-if="locations.length === 0 && !loading">暂无数据</text> -->
					<view class="empty-guide" v-if="locations.length === 0 && !loading">
						<image src="../../static/images/空状态.png" mode="widthFix" />
						<view class="empty-text">暂无场地数据</view>
						<button class="publish-btn" @click="addAddress">发布场地</button>
					</view>
				</view>
			</scroll-view>
		</view>

		<!-- 运动选择弹窗 -->
		<view class="sport-modal" v-if="showSportModal" @tap="closeSportModal">
			<view class="sport-options" @tap.stop>
				<view class="modal-header">
					<text class="modal-title">选择感兴趣的运动</text>
					<text class="modal-subtitle">最多可选择5项</text>
				</view>
				<view class="sport-grid">
					<view v-for="sport in sports" :key="sport.id" class="sport-item"
						:class="{ selected: selectedSports.some(item => item.id == sport.id) }" @tap="toggleSport(sport)">
						{{ sport.sportsName }}
					</view>
				</view>
				<button class="confirm-btn" @tap="confirmSports" :disabled="selectedSports.length === 0">
					确定
				</button>
			</view>
		</view>

		<!-- 地图选择弹窗 -->
		<view class="map-modal" v-if="showMapModal" @tap="closeMapModal">
			<view class="map-options" @tap.stop>
				<view class="map-option" @click="navigate('gaode', selectedLocation)">
					<text class="iconfont icon-map"></text>
					高德地图
				</view>
				<view class="map-option" @click="navigate('baidu', selectedLocation)">
					<text class="iconfont icon-map"></text>
					百度地图
				</view>
			</view>
		</view>
		<!-- <image src="../../static/images/空状态.png" mode="widthFix" class="empty-image" v-if="emptyImage && !loading" /> -->
		<!-- 添加球场 -->
		<view class="addAddress" @click="addAddress">
			+
		</view>
	</view>
</template>

<script>
import {
	getLocationList,getAllSports
} from "../../Api/index.js";


import {
	debounce
} from '@/utools/index.js'
import Mixins from "../../minix/index.js"
export default {
	mixins: [Mixins],
	data() {
		return {
			scrollTop:0,
			top: 0,
			showShareModal: false,
			emptyImage: false,
			longitude: "",
			latitude: "",
			formData: {
				latitude: "",
				longitude: "",
				selectedDistanceIndex: 0,
				selectedTypeIndex: 0,
				page: 1,
				pageSize: 5, // 减少首屏数据量，提高加载速度
				type: ''
			},
			refreshTriggered: false, // 下拉刷新状态
			loading: true,
			hasMore: true,
			locationsHeight: '',
			loadedIds: [], // 用于跟踪已加载的地点ID
			// 运动类型列表
			sports: [],
			// 用户选择的运动类型
			selectedSports: [],
			// 当前选中的运动类型
			currentSport: {},
			// 是否显示运动选择弹窗
			showSportModal: false,
			// 是否显示地图选择弹窗
			showMapModal: false,
			// 当前选中的地点
			selectedLocation: null,
			// 场地列表数据
			locations: [],
			distanceOptions: ['默认', '1Km内', '2km内', '3Km内', '4Km内', '5Km内', '6Km内', '7Km内', '8Km内', '9Km内', '10Km内',
				'11Km内', '12Km内', '13Km内', '14Km内', '15Km内', '16Km内', '17Km内', '18Km内', '19Km内', '20Km内'
			],
			typeOptions: ['默认', '免费', '未知', '按次', '按小时'],
			isReady: false,
		}
	},

	mounted() {
		const startTime = Date.now()
		this.isReady = false

		// 优化：并行执行初始化操作
		Promise.all([
			this.initSports(),
			this.initNavBar(),
			this.getCurrentLocation()
		]).then(() => {
			const loadTime = Date.now() - startTime
			console.log(`页面初始化完成，耗时: ${loadTime}ms`)

			// 如果加载时间过长，给出提示
			if (loadTime > 3000) {
				console.warn('页面加载时间较长，建议检查网络连接')
			}
		}).catch(err => {
			console.error('初始化失败:', err)
			uni.showToast({
				title: '获取位置失败，请检查后重试',
				icon: 'none'
			})
		})

		// 延迟显示分享诱导弹窗
		this.checkAndShowShareModal()
	},
	watch: {
		"$store.state.sportsWord": {
			handler(newval) {
				this.sports = [...newval]
			},
			deep: true,
		},
		currentSport(newval) {
			this.loading = true
			this.formData.type = newval.id
			this.locations=[]
			this.formData.page=1
			this.$nextTick(()=>{
				this.scrollTop = 0
			})
		},
		formData: {
			handler(newval) {
				if (this.isReady) {
					this.getLocationListData(newval)
				}
			},
			deep: true,
		},
	},
	computed: {

	},
	onLoad() {
		this.isReady = false

		// 优化：先设置运动类型，减少等待时间
		this.initSelectedSports()

		// 监听运动选择变化
		uni.$on('updateSports', (sports) => {
			this.selectedSports = sports
			if (!sports.includes(this.currentSport) && sports.length > 0) {
				this.currentSport = sports[0]
			}
		})
	},
	onShow() {

		let data = uni.getStorageSync('selectedSports')

		if (data) {
			data = JSON.parse(data)

			if (this.areArraysEqualById(data, this.selectedSports)) {
				// 检查全局标记，判断是否需要刷新数据
				const app = getApp();
				if (app.globalData && app.globalData.needRefreshHomeData) {
					this.onRefresh(); // 刷新数据
					app.globalData.needRefreshHomeData = false; // 重置标记
				}
				return false
			}
			this.selectedSports = data
			this.currentSport = this.selectedSports[0]
		}



	},
	onUnload() {
		uni.$off('updateSports')
	},

	methods: {
		// 初始化运动数据
		async initSports() {
			try {
				const res = await getAllSports()
				if (res.data.code === 200) {
					this.$store.commit('SAVEWORD', res.data.data.sports)
				}
			} catch (err) {
				console.error('获取运动数据失败:', err)
			}
		},

		// 初始化导航栏
		initNavBar() {
			return new Promise((resolve) => {
				this.$nextTick(() => {
					const res = uni.getMenuButtonBoundingClientRect()
					const statusHeight = res.top
					const navHeight = res.height
					this.top = statusHeight + navHeight + 10
					resolve()
				})
			})
		},

		areArraysEqualById(arr1, arr2) {
			// 首先检查数组长度是否相同
			if (arr1.length !== arr2.length) {
				return false;
			}

			// 使用Set存储第二个数组的所有id
			const idSet = new Set(arr2.map(item => item.id));

			// 检查第一个数组的每个元素的id是否都存在于Set中
			return arr1.every(item => idSet.has(item.id));
		},
		lookDetail(item) {
			uni.navigateTo({
				url: "/subpages/location-detail/location-detail?item=" + encodeURIComponent(JSON.stringify(item))
			})
		},
		getDistanceMeters(m) {
			if (m < 1000) {
				return Math.round(m) + '米';
			} else {
				return (m / 1000).toFixed(1) + '公里';
			}
		},
		// 获取当前定位
		getCurrentLocation() {
			return new Promise((resolve, reject) => {
				// 先检查缓存的位置信息
				const cachedLocation = uni.getStorageSync('userLocation')
				const cacheTime = uni.getStorageSync('locationCacheTime')
				const now = Date.now()

				// 如果缓存存在且未过期（3分钟内），直接使用缓存
				if (cachedLocation && cacheTime && (now - cacheTime < 3 * 60 * 1000)) {
					this.formData.latitude = cachedLocation.latitude
					this.formData.longitude = cachedLocation.longitude
					this.isReady = true
					resolve(cachedLocation)
					return
				}

				uni.getLocation({
					type: 'gcj02',
					success: (res) => {
						this.formData.latitude = res.latitude
						this.formData.longitude = res.longitude
						this.isReady = true

						// 缓存位置信息
						uni.setStorageSync('userLocation', {
							latitude: res.latitude,
							longitude: res.longitude
						})
						uni.setStorageSync('locationCacheTime', now)

						resolve(res)
					},
					fail: (err) => {
						console.error('获取位置失败:', err)
						this.isReady = true
						reject(err)
					}
				})
			})
		},
		// 下拉刷新
		onRefresh() {
			// 重置页码
			this.formData.page = 1
			// 清空已有列表数据
			this.locations = []
			// 设置刷新状态为true
			this.refreshTriggered = true

			// 重新获取数据
			this.getLocationListData(this.formData)
		},
		// 获取当前位置对应的场地列表
		getLocationListData: debounce(function (data) {
			if (this.formData.type == '') {
				if (this.refreshTriggered) {
					this.refreshTriggered = false
				}
				return false
			}

			this.loading = true

			let distanceOptions = ['', '1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12', '13',
				'14', '15', '16', '17', '18', '19', '20'
			]
			let typeOptions = ['', 'free', 'unknown', 'perTime', 'hourly']
			data.distance = distanceOptions[data.selectedDistanceIndex]
			data.feeType = typeOptions[data.selectedTypeIndex]

			// 添加请求缓存键
			const cacheKey = `locationList_${JSON.stringify(data)}`
			const cachedData = uni.getStorageSync(cacheKey)
			const cacheTime = uni.getStorageSync(`${cacheKey}_time`)
			const now = Date.now()

			// 如果缓存存在且未过期（2分钟内），使用缓存数据
			if (cachedData && cacheTime && (now - cacheTime < 2 * 60 * 1000) && data.page === 1) {
				this.processLocationData(cachedData)
				this.loading = false
				this.refreshTriggered = false
				return
			}

			getLocationList(data).then(res => {
				let arr = res.data.data.locations

				// 缓存首页数据
				if (data.page === 1) {
					uni.setStorageSync(cacheKey, arr)
					uni.setStorageSync(`${cacheKey}_time`, now)
				}

				this.processLocationData(arr)
				this.loading = false
				this.refreshTriggered = false
			}).catch(() => {
				this.loading = false
				this.refreshTriggered = false
			})
		}, 300), // 减少防抖时间，提高响应速度

		// 处理位置数据的公共方法
		processLocationData(arr) {
			arr.forEach(ele => {
				// 确保images是数组，只保留第一张图片以提高加载速度
				if (typeof ele.images == 'string') {
					try {
						ele.images = JSON.parse(ele.images)
						// 首屏只显示第一张图片，减少加载时间
						if (ele.images.length > 1) {
							ele.images = [ele.images[0]]
						}
					} catch (e) {
						ele.images = []
					}
				}
			});

			this.locations = [...this.locations, ...arr]
			this.emptyImage = this.locations.length === 0
			this.hasMore = arr.length >= this.formData.pageSize
		},

		// 加载更多数据
		loadMore: debounce(function() {
			if (!this.hasMore || this.loading) return

			this.formData.page += 1
			this.loading = true
			this.getLocationListData(this.formData)
		}, 100), // 减少防抖时间，提高响应速度

		// 添加运动场地
		addAddress() {
			if (this.$store.state.userinfo.nickname) {
				uni.navigateTo({
					url: "/subpages/add-location/add-location"
				})
			} else {
				uni.reLaunch({
					url: "/pages/profile/profile?flag=1"
				});

			}

		},
		// 显示运动选择弹窗
		sportModal() {
			this.showSportModal = true
			if (this.sports.length == 0) {
				// this.getAllSportsData()
			}
		},
		// 关闭运动选择弹窗
		closeSportModal() {
			this.showSportModal = false
		},
		// 切换运动选择状态
		toggleSport(sport) {
			const index = this.selectedSports.findIndex(item => item.id == sport.id);
			if (index > -1) {
				this.selectedSports.splice(index, 1)
			} else if (this.selectedSports.length < 5) {
				this.selectedSports.push(sport)
			} else {
				uni.showToast({
					title: '最多选择5项运动',
					icon: 'none'
				})
			}
		},
		// 确认选择的运动
		confirmSports() {
			if (this.selectedSports.length === 0) {
				uni.showToast({
					title: '请至少选择一项运动',
					icon: 'none'
				})
				return
			}
			// 保存选择
			uni.setStorageSync('selectedSports', JSON.stringify(this.selectedSports))
			// 设置当前选中的运动
			this.currentSport = this.selectedSports[0]
			// 关闭弹窗
			this.closeSportModal()
			// 触发全局事件，通知附近页面更新
			uni.$emit('updateSports', this.selectedSports)
			this.locations = []
			this.getLocationListData(this.formData)

		},
		// 选择运动类型
		selectSport(sport) {
			if (this.currentSport.id === sport.id) return
			this.scrollTop=100
			this.currentSport = sport
			
		},
		// 显示地图选择弹窗
		showMapOptions(location) {
			// this.selectedLocation = location
			// this.showMapModal = true
			uni.openLocation({
				latitude: Number(location.latitude),
				longitude: Number(location.longitude),
				name: location.name,
				address: location.address,
				scale: 14
			});
		},
		// 关闭地图选择弹窗
		closeMapModal() {
			this.showMapModal = false
			this.selectedLocation = null
		},
		// 导航到指定地点
		navigate(type, location) {
			if (!location || !location.latitude || !location.longitude) {
				uni.showToast({
					title: '位置信息不完整',
					icon: 'none'
				})
				return
			}

			const {
				latitude,
				longitude,
				name
			} = location

			if (type === 'gaode') {
				// 打开高德地图
				uni.openLocation({
					latitude: Number(latitude),
					longitude: Number(longitude),
					name,
					success: () => {
						this.closeMapModal()
					},
					fail: (err) => {
						console.log(err);

						uni.showToast({
							title: '请先安装高德地图',
							icon: 'none'
						})
					}
				})
			} else if (type === 'baidu') {
				// 打开百度地图
				uni.openLocation({
					latitude: Number(latitude),
					longitude: Number(longitude),
					name,
					success: () => {
						this.closeMapModal()
					},
					fail: (err) => {
						console.log(err);

						uni.showToast({
							title: '请先安装高德地图',
							icon: 'none'
						})
					}
				})
			}
		},
		// 导航到指定地点
		onDistanceChange(e) {
			this.formData.selectedDistanceIndex = Number(e.detail.value)
			this.formData.page = 1
			this.locations = []
		},
		onTypeChange(e) {
			this.formData.selectedTypeIndex = Number(e.detail.value)
			this.formData.page = 1
			this.locations = []
		},

		// 图片加载错误处理
		handleImageError(e) {
			console.log('图片加载失败:', e)
			// 可以设置默认图片
			e.target.src = '/static/logo.png'
		},

		// 图片加载成功处理
		handleImageLoad() {
			// 图片加载成功后的处理
		},

		// 初始化选中的运动类型
		initSelectedSports() {
			const hasSelectedSports = uni.getStorageSync('selectedSports')
			if (!hasSelectedSports) {
				this.showSportModal = true
			} else {
				try {
					this.selectedSports = JSON.parse(hasSelectedSports)
					if (this.selectedSports.length > 0) {
						this.currentSport = this.selectedSports[0]
					}
				} catch (e) {
					console.error('解析运动类型数据失败:', e)
					this.showSportModal = true
				}
			}
		},

		// 检查并显示分享弹窗
		checkAndShowShareModal() {
			const hasSeenShareModal = uni.getStorageSync('hasSeenShareModal')
			const lastShowTime = uni.getStorageSync('lastShareModalTime')
			const now = Date.now()

			// 如果从未显示过，或者距离上次显示超过7天，则显示分享弹窗
			if (!hasSeenShareModal || (lastShowTime && now - lastShowTime > 7 * 24 * 60 * 60 * 1000)) {
				setTimeout(() => {
					this.showShareModal = true
				}, 5000) // 5秒后显示
			}
		},

		// 关闭分享弹窗
		closeShareModal() {
			this.showShareModal = false
			// 记录用户已经看过分享弹窗和时间
			uni.setStorageSync('hasSeenShareModal', true)
			uni.setStorageSync('lastShareModalTime', Date.now())
		},

		// 分享给朋友
		shareToFriend() {
			this.closeShareModal()
		},

		// 添加到我的小程序
		addToMiniProgram() {
			// #ifdef MP-WEIXIN
			uni.showToast({
				title: '请点击右上角"..."添加到我的小程序',
				icon: 'none',
				duration: 3000
			})
			// #endif

			// #ifdef H5
			uni.showToast({
				title: '请将网页添加到收藏夹',
				icon: 'none',
				duration: 3000
			})
			// #endif

			this.closeShareModal()
		}
	}
}
</script>

<style lang="scss" scoped>
/* 分享弹窗样式 */
.share-modal {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.6);
	display: flex;
	justify-content: center;
	align-items: center;
	z-index: 9999;
	backdrop-filter: blur(4rpx);
}

.share-content {
	background: #fff;
	border-radius: 32rpx;
	width: 85%;
	max-width: 600rpx;
	padding: 60rpx 40rpx 40rpx;
	position: relative;
	animation: shareModalShow 0.3s ease-out;
}

@keyframes shareModalShow {
	from {
		opacity: 0;
		transform: scale(0.8) translateY(50rpx);
	}
	to {
		opacity: 1;
		transform: scale(1) translateY(0);
	}
}

.share-header {
	text-align: center;
	margin-bottom: 40rpx;
}

.share-logo {
	width: 120rpx;
	height: 120rpx;
	border-radius: 24rpx;
	margin-bottom: 20rpx;
}

.share-title {
	font-size: 36rpx;
	font-weight: 600;
	color: #333;
	margin-bottom: 12rpx;
}

.share-subtitle {
	font-size: 28rpx;
	color: #666;
	line-height: 1.4;
}

.share-benefits {
	margin-bottom: 50rpx;
}

.benefit-item {
	display: flex;
	align-items: center;
	margin-bottom: 24rpx;
	padding: 0 20rpx;
}

.benefit-icon {
	font-size: 32rpx;
	margin-right: 20rpx;
	width: 40rpx;
	text-align: center;
}

.benefit-text {
	font-size: 28rpx;
	color: #555;
	flex: 1;
}

.share-actions {
	margin-bottom: 30rpx;
}

.share-btn {
	width: 100%;
	height: 88rpx;
	border-radius: 44rpx;
	font-size: 32rpx;
	font-weight: 500;
	margin-bottom: 20rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	border: none;
	position: relative;
	overflow: hidden;
}

.share-btn.primary {
	background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
	color: #fff;
	box-shadow: 0 8rpx 20rpx rgba(76, 175, 80, 0.3);
}

.share-btn.secondary {
	background: #f8f9fa;
	color: #333;
	border: 2rpx solid #e9ecef;
}

.share-btn-icon {
	margin-right: 12rpx;
	font-size: 28rpx;
}

.share-close {
	text-align: center;
	padding: 20rpx;
}

.close-text {
	font-size: 28rpx;
	color: #999;
	text-decoration: underline;
}

.empty-image {
	position: fixed;
	width: 60%;
	left: 50%;
	transform: translateX(-50%);
	top: 30%;
}

/* 骨架屏样式 */
.skeleton {
	padding: 0;
}

.skeleton-card {
	background: #fff;
	border-radius: 16rpx;
	margin-bottom: 30rpx;
	overflow: hidden;
	box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.skeleton-image {
	width: 100%;
	height: 300rpx;
	background: #f2f2f2; /* 简化背景，移除动画以提高性能 */
}

.skeleton-info {
	padding: 20rpx;
}

.skeleton-title {
	height: 32rpx;
	width: 60%;
	background: #f2f2f2;
	margin-bottom: 14rpx;
	border-radius: 4rpx;
}

.skeleton-line {
	height: 24rpx;
	width: 80%;
	background: #f2f2f2;
	margin-bottom: 10rpx;
	border-radius: 4rpx;
}

.skeleton-footer {
	display: flex;
	align-items: center;
	justify-content: space-between;
	margin-top: 20rpx;
}

.skeleton-distance {
	height: 24rpx;
	width: 100rpx;
	background: #f2f2f2;
	border-radius: 4rpx;
}

.skeleton-btn {
	height: 60rpx;
	width: 120rpx;
	background: #f2f2f2;
	border-radius: 40rpx;
}

.container {
	height: 100vh;
	background-color: #f5f5f5;
	display: flex;
	flex-direction: column;

	.addAddress {
		width: 100rpx;
		height: 100rpx;
		position: fixed;
		bottom: 120rpx;
		right: 20rpx;
		border-radius: 50%;
		background-color: #4CAF50;
		display: flex;
		justify-content: center;
		align-items: center;
		font-size: 34rpx;
		font-weight: bold;
		color: #fff;
	}
}

.nav-bar {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	z-index: 999;
	background: #4CAF50;
}

.header {
	padding: 20rpx 30rpx;
	text-align: center;
	position: relative;

	.title {
		color: #fff;
		font-size: 36rpx;
		font-weight: 500;
	}
}

.page-content {
	padding-top: calc(var(--status-bar-height) + 88rpx);
	flex: 1;
	overflow: hidden;
}

.filter-bar {
	display: flex;
	align-items: center;
	justify-content: space-around;
	background: #fff;
	padding: 0 10rpx;
	border-bottom: 1rpx solid #eee;
	margin-bottom: 0;
	min-height: 70rpx;

	.filter-item {
		display: inline-flex;
		align-items: center;
		padding: 10rpx 32rpx;
		margin-right: 20rpx;
		border-radius: 40rpx;
		background: #f0f0f0;
		font-size: 26rpx;
		color: #666;
		border: none;
		transition: background 0.2s;
	}

	// picker 选中时高亮
	picker[show] .filter-item,
	.filter-item.active {
		background: #4CAF50;
		color: #fff;
	}
}

.sports-tabs {
	display: flex;
	padding: 10rpx;
	background: #fff;
	white-space: nowrap;
	border-bottom: 1rpx solid #eee;
	align-items: center;

	.sport-tab {
		display: inline-block;
		padding: 14rpx 32rpx;
		margin-right: 20rpx;
		border-radius: 40rpx;
		background: #f0f0f0;
		font-size: 26rpx;
		color: #666;

		&.active {
			background: #4CAF50;
			color: #fff;
		}
	}

	.add-sport-btn {
		display: inline-flex;
		align-items: center;
		justify-content: center;
		width: 70rpx;
		height: 70rpx;
		border-radius: 40rpx;
		background: #f0f0f0;
		margin-left: 20rpx;

		.plus {
			font-size: 40rpx;
			color: #666;
			line-height: 1;
		}
	}
}

.locations {
	padding: 30rpx 0;
	height: calc(100vh - 270rpx);
	overflow: hidden;
}

.location-desc {
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
	font-size: 24rpx;
	color: #666;
	margin-bottom: 8rpx;
}

.map-modal {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.5);
	display: flex;
	justify-content: center;
	align-items: center;

	.map-options {
		background: #fff;
		padding: 40rpx;
		border-radius: 24rpx;
		width: 80%;
		max-width: 600rpx;

		.map-option {
			padding: 24rpx;
			margin: 16rpx 0;
			border: 1rpx solid #ddd;
			border-radius: 16rpx;
			display: flex;
			align-items: center;

			.iconfont {
				margin-right: 16rpx;
				font-size: 32rpx;
				color: #666;
			}
		}
	}
}

.sport-modal {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.5);
	display: flex;
	justify-content: center;
	align-items: center;
	z-index: 1000;

	.sport-options {
		background: #fff;
		padding: 40rpx;
		border-radius: 24rpx;
		width: 80%;
		max-width: 600rpx;

		.modal-header {
			text-align: center;
			margin-bottom: 40rpx;

			.modal-title {
				font-size: 32rpx;
				font-weight: 500;
				color: #333;
				margin-bottom: 8rpx;
				display: block;
			}

			.modal-subtitle {
				font-size: 24rpx;
				color: #999;
			}
		}

		.sport-grid {
			display: grid;
			grid-template-columns: repeat(3, 1fr);
			gap: 20rpx;
			margin-bottom: 40rpx;

			.sport-item {
				height: 80rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				background: #f5f5f5;
				border-radius: 12rpx;
				font-size: 28rpx;
				color: #666;

				&.selected {
					background: #4CAF50;
					color: #fff;
				}
			}
		}

		.confirm-btn {
			width: 100%;
			height: 88rpx;
			background: #4CAF50;
			color: #fff;
			font-size: 32rpx;
			border-radius: 44rpx;

			&[disabled] {
				background: #ccc;
			}
		}
	}
}

.loading-status {
	text-align: center;
	padding: 20rpx 0;
	color: #999;
	font-size: 24rpx;
}

.location-card {
	background: #fff;
	border-radius: 16rpx;
	margin: 0 30rpx;
	margin-bottom: 30rpx;
	overflow: hidden;
	box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.location-image {
	width: 100%;
	height: 300rpx;
}

.location-image-item {
	width: 100%;
	height: 100%;
}

.location-info {
	padding: 20rpx;
}

.location-title {
	display: flex;
	flex-direction: row;
	align-items: center;
	justify-content: space-between;
	margin-bottom: 10rpx;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
}

.location-name {
	font-size: 32rpx;
	font-weight: 500;
	color: #333;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
	flex: 1;
}

.price {
	font-size: 28rpx;
	font-weight: 500;
	color: #FF6B00;
	margin-left: 10rpx;
	flex-shrink: 0;

	&.free {
		color: #4CAF50;
	}
}

.location-footer {
	display: flex;
	align-items: center;
	justify-content: space-between;
	margin-top: 10rpx;
	margin-bottom: 10rpx;

	.distance {
		font-size: 24rpx;
		color: #999;
		text-align: left;
	}

	.navigate-btn {
		margin: 0;
		background: #4CAF50;
		color: #fff;
		// padding: 16rpx 32rpx;
		// border-radius: 40rpx;
		font-size: 28rpx;
		display: flex;
		align-items: center;

		.iconfont {
			margin-right: 8rpx;
		}
	}
}

.location-meta {
	display: flex;
	align-items: center;
	justify-content: space-between;
	margin-top: 20rpx;
}

.distance {
	font-size: 24rpx;
	color: #666;
}



.iconfont {
	margin-right: 6rpx;
}
.empty-guide {
		position: fixed;
		top: 30%;
		left: 50%;
		transform: translate(-50%, -50%);
		// background: rgba(255, 255, 255, 0.8);
		text-align: center;

		.empty-text {
			font-size: 28rpx;
			color: #666;
			margin: 20rpx 0;
			margin-top: -50rpx;
		}

		.publish-btn {
			background: #4CAF50;
			color: #fff;
			width: fit-content;
			padding: 10rpx 32rpx;
			border-radius: 40rpx;
			font-size: 32rpx;
		}
	}
</style>