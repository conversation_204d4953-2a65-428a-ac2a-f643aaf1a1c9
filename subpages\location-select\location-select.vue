<template>
	<view class="container">
		<!-- 顶部导航栏 -->
		<view class="nav-bar">
			<view class="status-bar"></view>
			<view class="header">
				<view class="back-btn" @tap="goBack">
					<view class="back-icon">
						<view class="back-arrow"></view>
					</view>
				</view>
				<text class="title">约球详情</text>
			</view>
		</view>

		<!-- 页面内容区域 -->
		<view class="page-content">
			<!-- 搜索框 -->
			<view class="search-box">
				<view class="search-input-box">
					<text class="search-icon">🔍</text>
					<input class="search-input" type="text" v-model="searchKeyword" placeholder="搜索场地名称" @input="onSearchInput"
						@focus="onSearchFocus" @blur="onSearchBlur" confirm-type="search" @confirm="onSearchConfirm" />
					<text class="clear-icon" v-if="searchKeyword" @tap="clearSearch">×</text>
				</view>
			</view>

			<!-- 筛选条 -->
			<view class="filter-bar" >
				<picker :range="distanceOptions" @change="onDistanceChange" v-if="!isSearching">
					<view class="filter-item">
						距离：{{ distanceOptions[formData.selectedDistanceIndex] }}
					</view>
				</picker>
				<picker :range="typeOptions" @change="onTypeChange" v-if="!isSearching">
					<view class="filter-item">
						类型：{{ typeOptions[formData.selectedTypeIndex] }}
					</view>
				</picker>
				<picker :range="sportOptions" @change="onSportChange" v-if="false">
					<view class="filter-item">
						运动：{{ sportOptions[formData.selectedSportIndex] }}
					</view>
				</picker>
				<text style="color: #fff;">筛选</text>
			</view>

			<!-- 场地列表 -->
			<scroll-view :scroll-top="scrollTop" class="locations" scroll-y="true" lower-threshold="100"
				@scrolltolower="loadMore" refresher-enabled="true" :refresher-triggered="refreshTriggered"
				@refresherrefresh="onRefresh">
				<!-- 骨架屏 -->
				<view v-if="loading && locations.length === 0" class="skeleton">
					<view v-for="i in 3" :key="i" class="skeleton-card">
						<view class="skeleton-image"></view>
						<view class="skeleton-info">
							<view class="skeleton-title"></view>
							<view class="skeleton-line"></view>
							<view class="skeleton-line"></view>
							<view class="skeleton-footer">
								<view class="skeleton-distance"></view>
								<view class="skeleton-btn"></view>
							</view>
						</view>
					</view>
				</view>

				<!-- 实际内容 -->
				<view v-else v-for="(location, index) in locations" :key="index" class="location-card"
					@tap="selectLocation(location)">
					<image :src="location.images && location.images.length > 0 ? location.images[0] : '/static/logo.png'"
						mode="aspectFill" class="location-image" />
					<view class="location-info">
						<view class="location-title">
							<text class="location-name">{{ location.name }}</text>
							<text v-if="location.feeType == 'free'" class="price free">免费</text>
							<text v-else-if="location.feeType == 'unknown'" class="price free">未知</text>
							<text v-else-if="location.feeType == 'hourly'" class="price">¥{{ location.hourlyPrice }}/小时</text>
							<text v-else-if="location.feeType == 'perTime'" class="price">¥{{ location.perTimePrice }}/次</text>
						</view>
						<view class="location-desc">{{ location.description }}</view>
						<view class="location-desc">{{ location.address }}</view>
						<view class="location-footer">
							<text class="distance" v-if="location.distance_meters">{{ getDistanceMeters(location.distance_meters)
								}}</text>
							<text class="distance" v-else>{{
								$getDistance(location.latitude, location.longitude, formData.latitude, formData.longitude) }}</text>
							<view class="sport-tags">
								<text class="sport-tag" v-for="(sport, sportIndex) in location.sports" :key="sportIndex">
									{{ getSportName(sport) }}
								</text>
							</view>
						</view>
					</view>
				</view>

				<!-- 加载状态提示 -->
				<view class="loading-status">
					<text v-if="loading && locations.length > 0">正在加载中...</text>
					<text v-else-if="!hasMore && locations.length > 0">没有更多数据了</text>
					<view class="empty-guide" v-if="locations.length === 0 && !loading">
						<image src="../../static/images/空状态.png" mode="widthFix" />
						<view class="empty-text">暂无场地数据</view>
						<button class="publish-btn" @tap="addLocation">发布场地</button>
					</view>
				</view>
			</scroll-view>
		</view>
	</view>
</template>

<script>
import {
	getLocationList, searchLocations
} from "../../Api/index.js";

import {
	debounce
} from '@/utools/index.js'
import Mixins from "../../minix/index.js"

export default {
	mixins: [Mixins],
	data() {
		return {
			scrollTop: 0,
			searchKeyword: '',
			isSearching: false, // 是否正在搜索状态
			formData: {
				latitude: "",
				longitude: "",
				selectedDistanceIndex: 0,
				selectedTypeIndex: 0,
				selectedSportIndex: 0,
				page: 1,
				pageSize: 10,
				keyword: '',
				type: '',
				feeType: '',
				distance: "20"
			},
			refreshTriggered: false, // 下拉刷新状态
			loading: true,
			hasMore: true,
			// 场地列表数据
			locations: [],
			distanceOptions: ['不限距离', '1Km内', '2km内', '3Km内', '5Km内', '10Km内', '20Km内'],
			typeOptions: ['默认', '免费', '未知', '按次', '按小时'],
			sportOptions: ['所有运动'],
			sports: [],
			isReady: false,
			searchTimer: null
		}
	},
	onLoad(options) {
		this.formData.type = options.type || ''
	},
	mounted() {
		this.isReady = false
		this.getCurrentLocation()

		// 获取运动类型列表
		this.sports = this.$store.state.sportsWord || []

		// 构建运动选项
		if (this.sports.length > 0) {
			this.sportOptions = ['所有运动']
			this.sports.forEach(sport => {
				this.sportOptions.push(sport.sportsName)
			})
		}
	},

	methods: {
		// 获取当前位置
		getCurrentLocation() {
			uni.getLocation({
				type: 'gcj02',
				success: (res) => {
					this.formData.latitude = res.latitude
					this.formData.longitude = res.longitude
					this.isReady = true
					this.getLocationsData(this.formData)
				},
				fail: () => {
					uni.showToast({
						title: '获取位置失败，请检查定位权限',
						icon: 'none'
					})
					this.isReady = true
				}
			})
		},

		// 获取场地列表数据
		getLocationsData: debounce(function (data) {
			if (!data.latitude) {
				return false
			}

			this.loading = true

			// 根据是否有搜索关键词决定使用哪个API
			const apiMethod = data.keyword ? searchLocations : getLocationList
			if (!data.keyword) {
				delete data.keyword
			}
			apiMethod(data).then(res => {
				if (res.data.code === 200) {
					let arr = res.data.data.locations || []

					arr.forEach(ele => {
						// 确保images是数组
						if (typeof ele.images === 'string') {
							try {
								ele.images = JSON.parse(ele.images)
							} catch (e) {
								// 如果解析失败，设为空数组
								ele.images = []
							}
						}
					});

					// 如果是第一页，直接替换
					if (data.page === 1) {
						this.locations = arr
					} else {
						// 否则追加
						this.locations = [...this.locations, ...arr]
					}

					// 判断是否还有更多数据
					this.hasMore = arr.length >= data.pageSize
				} else {
					uni.showToast({
						title: res.data.message || '获取数据失败',
						icon: 'none'
					})
				}

				this.loading = false
				this.refreshTriggered = false
			}).catch(() => {
				this.loading = false
				this.refreshTriggered = false
				uni.showToast({
					title: '网络错误，请稍后重试',
					icon: 'none'
				})
			})
		}, 500),

		// 返回上一页
		goBack() {
			uni.navigateBack()
		},

		// 清除搜索
		async clearSearch() {
			this.scrollTop=100
			this.searchKeyword = ''
			this.formData.keyword = ''
			this.formData.page = 1
			this.isSearching = false
			await this.getLocationsData(this.formData)
			this.$nextTick(()=>{
				this.scrollTop = 0
			})
		},

		// 搜索框聚焦
		onSearchFocus() {
			// 当搜索框聚焦时，如果有搜索内容则显示为搜索状态
			if (this.searchKeyword) {
				this.isSearching = true
			}
		},

		// 搜索框失焦
		onSearchBlur() {
			// 搜索框失焦时，如果没有搜索内容则退出搜索状态
			if (!this.searchKeyword) {
				this.isSearching = false
			}
		},

		// 搜索确认（键盘搜索按钮点击）
		async onSearchConfirm() {
			if (this.searchKeyword.trim()) {
				this.scrollTop=100
				this.isSearching = true
				this.formData.keyword = this.searchKeyword
				this.formData.page = 1
				await this.getLocationsData(this.formData)
				this.$nextTick(() => {
					this.scrollTop = 0
				})
			}
		},

		// 搜索输入
		onSearchInput: debounce(function () {
			if (this.searchKeyword.trim()) {
				// this.isSearching = true
				this.formData.keyword = this.searchKeyword
			} else {
				// this.isSearching = false
				this.formData.keyword = ''
			}
			this.formData.page = 1
			// this.getLocationsData(this.formData)
		}, 500),

		// 距离选择变更
		onDistanceChange(e) {
			this.formData.selectedDistanceIndex = e.detail.value

			// 根据选择设置距离值
			if (e.detail.value == 0) {
				this.formData.distance = '' // 不限
			} else {
				// 根据选项设置具体距离值
				const distances = ['', 1, 2, 3, 5, 10, 20]
				this.formData.distance = distances[e.detail.value]
			}

			this.formData.page = 1
			this.getLocationsData(this.formData)
		},

		// 类型选择变更
		onTypeChange(e) {
			this.formData.selectedTypeIndex = e.detail.value
			let arr = ['', 'free', 'unknown', 'perTime', 'hourly']
			// 根据选择设置类型值
			this.formData.feeType = arr[e.detail.value]
			// if (e.detail.value == 0) {
			// 	this.formData.feeType = '' // 所有类型
			// } else if (e.detail.value == 1) {
			// 	this.formData.feeType = 'free' // 免费
			// } else if (e.detail.value == 2) {
			// 	this.formData.feeType = 'free' // 免费
			// } 

			this.formData.page = 1
			this.getLocationsData(this.formData)
		},

		// 运动类型选择变更
		onSportChange(e) {
			this.formData.selectedSportIndex = e.detail.value

			// 根据选择设置运动类型值
			if (e.detail.value == 0) {
				this.formData.type = '' // 所有运动
			} else {
				// 获取对应的运动ID
				this.formData.type = this.sports[e.detail.value - 1].id
			}

			this.formData.page = 1
			this.getLocationsData(this.formData)
		},

		// 下拉刷新
		onRefresh() {
			this.formData.page = 1
			this.refreshTriggered = true
			this.getLocationsData({ ...this.formData })
		},

		// 加载更多
		loadMore() {
			if (this.loading || !this.hasMore) return
			this.formData.page++
			this.getLocationsData({ ...this.formData })
		},

		// 获取距离文本
		getDistanceMeters(m) {
			if (!m) return '未知距离'
			if (m < 1000) {
				return Math.round(m) + '米'
			} else {
				return (m / 1000).toFixed(1) + '公里'
			}
		},

		// 获取运动名称
		getSportName(sportId) {
			const sport = this.sports.find(item => item.id === sportId)
			return sport ? sport.sportsName : ''
		},

		// 选择场地
		selectLocation(location) {
			// 通过事件通信将选中的场地信息传回上一页
			const eventChannel = this.getOpenerEventChannel()
			eventChannel.emit('selectLocation', {
				id: location.id,
				name: location.name,
				address: location.address,
				latitude: location.latitude,
				longitude: location.longitude,
				images: location.images
			})

			// 返回上一页
			uni.navigateBack()
		},

		// 添加场地
		addLocation() {
			uni.navigateTo({
				url: '/subpages/add-location/add-location'
			})
		}
	}
}
</script>

<style lang="scss" scoped>
.container {
	display: flex;
	flex-direction: column;
	height: 100vh;
	background-color: #f5f5f5;
}

.nav-bar {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	z-index: 999;
	background: #4CAF50;
}

.header {
	padding: 20rpx 30rpx;
	display: flex;
	align-items: center;
	position: relative;

	.back-btn {
		position: absolute;
		left: 30rpx;
		color: #fff;
		display: flex;
		align-items: center;
		justify-content: center;
		width: 48rpx;
		height: 48rpx;

		.back-icon {
			width: 36rpx;
			height: 36rpx;
			position: relative;

			.back-arrow {
				width: 20rpx;
				height: 20rpx;
				border-left: 4rpx solid #fff;
				border-bottom: 4rpx solid #fff;
				transform: rotate(45deg);
				position: absolute;
				left: 8rpx;
				top: 8rpx;
			}
		}
	}

	.title {
		flex: 1;
		text-align: center;
		color: #fff;
		font-size: 36rpx;
		font-weight: 500;
	}
}


.page-content {
	flex: 1;
	// margin-top: calc(var(--status-bar-height) + 44px);
}

.search-box {
	padding: 10px 15px;
	background-color: #ffffff;
}

.search-input-box {
	position: relative;
	display: flex;
	align-items: center;
	background-color: #f0f0f0;
	border-radius: 20px;
	padding: 0 15px;
	height: 36px;
}

.search-icon {
	margin-right: 5px;
	color: #999999;
}

.search-input {
	flex: 1;
	height: 36px;
	font-size: 14px;
}

.clear-icon {
	width: 20px;
	height: 20px;
	display: flex;
	align-items: center;
	justify-content: center;
	color: #999999;
	font-size: 16px;
}

.filter-bar {
	display: flex;
	padding: 10px 15px;
	background-color: #ffffff;
	border-bottom: 1px solid #f0f0f0;
}

.filter-item {
	margin-right: 20px;
	font-size: 14px;
	color: #666666;
	display: flex;
	align-items: center;
}

.locations {
	height: calc(100vh - var(--status-bar-height) - 44px - 56px - 44px);
}

.location-card {
	margin: 10px 15px;
	border-radius: 10px;
	background-color: #ffffff;
	box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
	overflow: hidden;
	display: flex;
}

.location-image {
	width: 120px;
	height: 120px;
	object-fit: cover;
}

.location-info {
	flex: 1;
	padding: 10px;
	display: flex;
	flex-direction: column;
}

.location-title {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 5px;
}

.location-name {
	font-size: 16px;
	font-weight: bold;
	color: #333333;
}

.price {
	font-size: 12px;
	padding: 2px 6px;
	border-radius: 4px;
	background-color: #ffebee;
	color: #f44336;
}

.price.free {
	background-color: #e8f5e9;
	color: #4CAF50;
}

.location-desc {
	font-size: 14px;
	color: #666666;
	margin-bottom: 5px;
	line-height: 1.4;
	display: -webkit-box;
	-webkit-box-orient: vertical;
	-webkit-line-clamp: 1;
	line-clamp: 1;
	overflow: hidden;
}

.location-footer {
	margin-top: auto;
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.distance {
	font-size: 14px;
	color: #999999;
}

.sport-tags {
	display: flex;
	flex-wrap: wrap;
	justify-content: flex-end;
}

.sport-tag {
	font-size: 12px;
	padding: 2px 6px;
	border-radius: 10px;
	background-color: #f0f0f0;
	color: #666666;
	margin-left: 5px;
	margin-bottom: 5px;
}

.loading-status {
	padding: 15px;
	text-align: center;
	color: #999999;
	font-size: 14px;
}

.empty-guide {
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 30px 0;
}

.empty-guide image {
	width: 150px;
	margin-bottom: 15px;
}

.empty-text {
	font-size: 16px;
	color: #999999;
	margin-bottom: 20px;
}

.publish-btn {
	background-color: #4CAF50;
	color: #ffffff;
	padding: 8px 20px;
	border-radius: 20px;
	font-size: 14px;
}

/* 骨架屏样式 */
.skeleton {
	padding: 10px 15px;
}

.skeleton-card {
	margin-bottom: 15px;
	background-color: #ffffff;
	border-radius: 10px;
	overflow: hidden;
	display: flex;
}

.skeleton-image {
	width: 120px;
	height: 120px;
	background-color: #f0f0f0;
}

.skeleton-info {
	flex: 1;
	padding: 10px;
}

.skeleton-title {
	height: 20px;
	width: 60%;
	background-color: #f0f0f0;
	margin-bottom: 10px;
	border-radius: 4px;
}

.skeleton-line {
	height: 16px;
	width: 100%;
	background-color: #f0f0f0;
	margin-bottom: 10px;
	border-radius: 4px;
}

.skeleton-footer {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-top: auto;
}

.skeleton-distance {
	height: 16px;
	width: 30%;
	background-color: #f0f0f0;
	border-radius: 4px;
}

.skeleton-btn {
	height: 24px;
	width: 60px;
	background-color: #f0f0f0;
	border-radius: 12px;
}
</style>