<template>
  <view class="container">
    <!-- 顶部导航栏 -->
    <view class="nav-bar">
      <view class="status-bar"></view>
      <view class="header">
        <text class="title">附近</text>
      </view>
    </view>

    <!-- 页面内容区域 -->
    <view class="page-content">
      <!-- 地图区域 -->
      <view class="map-container">
        <map id="nearbyMap" class="map" :latitude="formData.latitude" :longitude="formData.longitude" :markers="markers"
          :scale="13" :min-scale="11" max-scale="18" :enable-rotate="false" :enable-satellite="false"
          :enable-traffic="false" :enable-poi="true" :enable-building="true" show-location @markertap="onMarkerTap"
          @regionchange="onRegionChange" @updated="updated"></map>
        <!-- 距离提示 -->
        <view class="distance-tip">
          <text>搜索范围: 20公里</text>
        </view>
        <!-- 地图控件区域 -->
        <view class="map-controls">
          <view class="control-btn refresh-btn" @tap="refreshLocations" >
            <text class="iconfont icon-refresh">↻</text>
          </view>
          <view class="control-btn location-btn" @tap="moveToCurrentLocation">
            <text class="iconfont icon-location">⊙</text>
          </view>
        </view>
      </view>

      <!-- 运动类型筛选 -->
      <view class="filter-container">
        <scroll-view class="filter-tabs" scroll-x="true" show-scrollbar="false">
          <view v-for="(sport, index) in selectedSports" :key="index" class="filter-tab"
            :class="{ active: currentSport.id === sport.id }" @tap="selectSport(sport)">
            {{ sport.sportsName }}
          </view>
          <view class="add-sport-btn" @tap="sportModal" v-if="false">
            <text class="plus">+</text>
          </view>
        </scroll-view>
      </view>

      <!-- 场地列表 -->
      <view class="list-container" v-if="popupShow">
        <view class="close-btn" @tap="closePopup">×</view>
        <view class="location-card" @tap="selectLocation(clickLocation)">
          <swiper class="location-image" indicator-dots autoplay circular
            v-if="clickLocation.images && clickLocation.images.length > 0">
            <swiper-item v-for="(img, imgIndex) in clickLocation.images" :key="imgIndex">
              <image :src="img" mode="" class="location-image-item" lazy-load="true" />
            </swiper-item>
          </swiper>
          <image v-else :src="'/static/logo.png'" mode="aspectFill" class="location-image" />
          <view class="location-info">
            <view class="location-title">
							<text class="location-name">{{ clickLocation.name }}</text>
							<text v-if="clickLocation.feeType == 'free'" class="price free">免费</text>
							<text v-else-if="clickLocation.feeType == 'unknown'" class="price free">未知</text>
							<text v-else-if="clickLocation.feeType == 'hourly'" class="price">¥{{ clickLocation.hourlyPrice }}/小时</text>
							<text v-else-if="clickLocation.feeType == 'perTime'" class="price">¥{{ clickLocation.perTimePrice }}/次</text>
						</view>
            <view class="location-desc">{{ clickLocation.description }}</view>
            <view class="location-desc" v-if="clickLocation.address">{{ clickLocation.address }}</view>
            <view class="location-meta">
              <text class="distance">距离：{{ getDistanceMeters(clickLocation.distance_meters) }}</text>
              <button class="navigate-btn" @tap.stop="showMapOptions(clickLocation)">
                <text class="iconfont icon-navigation"></text>
                导航
              </button>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 地图选择弹窗 -->
    <view class="map-modal" v-if="showMapModal" @tap="closeMapModal">
      <view class="map-options" @tap.stop>
        <view class="map-option" @tap="navigate('gaode', selectedLocation)">
          <text class="iconfont icon-map"></text>
          高德地图
        </view>
        <view class="map-option" @tap="navigate('baidu', selectedLocation)">
          <text class="iconfont icon-map"></text>
          百度地图
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { getLocationList } from "../../Api/index.js";
import { debounce } from '@/utools/index.js'
import Mixins from "../../minix/index.js"
export default {
	  mixins:[Mixins],
  data() {
    return {
      updatedTimer: null,
      popupShow: false,
      clickLocation: {},
      formData: {
        latitude: "",
        longitude: "",
        selectedDistanceIndex: 0,
        selectedTypeIndex: 0,
        page: 1,
        pageSize: 100,
        type: '',
        distance: "20"
      },
      debounceTimer: null,
      // 当前位置
      latitude: 39.908692,
      longitude: 116.397477,
      // 运动类型列表
      sports: [],
      // 用户选择的运动类型
      selectedSports: [],
      // 当前选中的运动类型
      currentSport: {},
      // 是否显示运动选择弹窗
      showSportModal: false,
      // 是否显示地图选择弹窗
      showMapModal: false,
      // 当前选中的地点
      selectedLocation: null,
      // 地图标记点
      markers: [],
      // 当前地图缩放级别
      mapScale: 16,
      // 已加载区域记录，用于防止重复加载
      loadedAreas: [],
      // 场地列表数据
      locations: [
        {
          id: 1,
          name: '星光运动中心',
          description: '综合性运动场馆，包含多种运动项目',
          distance: '300m',
          image: '/static/logo.png',
          type: '乒乓球',
          latitude: 39.908692,
          longitude: 116.397477
        }
      ]
    }
  },
  watch: {
    currentSport(newval) {
      this.formData.type = newval.id
      // 直接调用获取数据
      this.getLocationListData({ ...this.formData })
    },
    // 移除对formData的监听，避免无限递归调用
  },
  onLoad() {
    // 获取当前位置
    this.getCurrentLocation()


  },
  onShow() {
    let data = uni.getStorageSync('selectedSports')
    if (data) {
      data = JSON.parse(data)
      if (this.areArraysEqualById(data, this.selectedSports)) {
        // 检查全局标记，判断是否需要刷新附近页数据
        const app = getApp();

        if (app.globalData && app.globalData.needRefreshNearbyData) {
          // 刷新附近数据
          this.getLocationListData({ ...this.formData });
          app.globalData.needRefreshNearbyData = false; // 重置标记
        }
        return false
      }
      this.selectedSports = data
      this.currentSport = this.selectedSports[0]
    }
  },
  methods: {
    areArraysEqualById(arr1, arr2) {
      // 首先检查数组长度是否相同
      if (arr1.length !== arr2.length) {
        return false;
      }

      // 使用Set存储第二个数组的所有id
      const idSet = new Set(arr2.map(item => item.id));

      // 检查第一个数组的每个元素的id是否都存在于Set中
      return arr1.every(item => idSet.has(item.id));
    },
    updated() {
      // 地图加载完成后更新标记点
      this.updateMarkers()
    },
    getDistanceMeters(m) {
      if (m < 1000) {
        return Math.round(m) + '米';
      } else {
        return (m / 1000).toFixed(1) + '公里';
      }
    },
    // 获取当前位置对应的场地列表
    getLocationListData: debounce(function (data) {
      
      if (!data.latitude) {
        return false
      }

      // 使用一个新对象，避免修改原始对象
      const requestData = {
        ...data,
        feeType: '',
        type: this.currentSport.id
      }

      getLocationList(requestData).then(res => {
        let arr = res.data.data.locations
        arr.forEach(ele => {
          // 确保images是数组
          if (typeof ele.images === 'string') {
            try {
              ele.images = JSON.parse(ele.images)
              // 不再限制只保留第一张图片
            } catch (e) {
              // 如果解析失败，设为空数组
              ele.images = []
            }
          }
        });
        this.locations = arr
        this.updateMarkers()

        // 判断是否还有更多数据
        this.hasMore = arr.length >= data.pageSize
      }).catch(() => {
        console.log('获取场地列表失败')
      })
    }, 500), // 添加防抖时间，避免频繁调用
    // 显示运动选择弹窗
    sportModal() {
      this.showSportModal = true
    },

    // 关闭运动选择弹窗
    closeSportModal() {
      this.showSportModal = false
    },
    // 切换运动选择状态
    toggleSport(sport) {
      const index = this.selectedSports.findIndex(item => item.id == sport.id);
      if (index > -1) {
        this.selectedSports.splice(index, 1)
      } else if (this.selectedSports.length < 5) {
        this.selectedSports.push(sport)
      } else {
        uni.showToast({
          title: '最多选择5项运动',
          icon: 'none'
        })
      }
    },
    // 获取当前位置
    getCurrentLocation() {
      uni.getLocation({
        type: 'gcj02',
        success: (res) => {
          this.formData.latitude = res.latitude
          this.formData.longitude = res.longitude
          this.isReady = true
          // 位置获取成功后手动调用数据加载
          this.getLocationListData({ ...this.formData })
        }
      })
    },
    // 更新地图标记点
    updateMarkers() {
      if (!this.locations || this.locations.length === 0) {
        return this.markers = []
      }

      this.markers = this.locations.map((location) => {
        // 根据不同价格类型生成价格文本
        let priceText = '';
        if (location.feeType === 'free') {
          priceText = '(免费开放)';
        } else if (location.feeType === 'unknown') {
          priceText = '(价格未知)';
        } else if (location.feeType === 'hourly') {
          priceText = `(¥${location.hourlyPrice}/小时)`;
        } else if (location.feeType === 'perTime') {
          priceText = `(¥${location.perTimePrice}/次)`;
        }
        
        return {
          id: location.id,
          latitude: location.latitude,
          longitude: location.longitude,
          title: location.name,
          width: 40,
          height: 40,
          clickable: true,
          callout: {
            content: location.name + priceText,
            color: '#333333',
            fontSize: 10,
            fontWeight: 'bold',
            bgColor: '#ffffff',
            padding: 8,
            borderRadius: 6,
            borderColor: '#4CAF50',
            borderWidth: 1,
            textAlign: 'center',
            display: 'BYCLICK'
          },
          iconPath: this.currentSport.id==1?"../../static/icons/ppq.png":this.currentSport.id==2?"../../static/icons/lq.png":this.currentSport.id==3?"../../static/icons/ymq.png":this.currentSport.id==4?"../../static/icons/zq.png":"../../static/icons/ppq.png",
        };
      })
    },
    // 选择运动类型
    selectSport(sport) {
      this.currentSport = sport
      // 选择新的运动类型后，重新加载数据
      this.getLocationListData({ ...this.formData })
    },
    // 选择地点
    selectLocation(location) {

      // 跳转到详情页面
      uni.navigateTo({
        url: `/subpages/location-detail/location-detail?item=${encodeURIComponent(JSON.stringify(location))}`
      })
    },
    // 点击地图标记
    onMarkerTap(e) {
      let id = e.detail.markerId
      let arr = this.locations.filter(loc => {
        return loc.id == id
      });
      if (arr.length) {
        this.clickLocation = arr[0]
        this.popupShow = true
      } else {
        this.clickLocation = []
      }

    },
    // 显示地图选择弹窗
    showMapOptions(location) {
      // this.selectedLocation = location
      // this.showMapModal = true
      uni.openLocation({
        latitude: Number(location.latitude),
        longitude: Number(location.longitude),
        name: location.name,
        address: location.address,
        scale: 14
      });
    },
    // 关闭地图选择弹窗
    closeMapModal() {
      this.showMapModal = false
      this.selectedLocation = null
    },
    // 导航到指定地点
    navigate(type, location) {
      const { latitude, longitude, name } = location
      if (type === 'gaode') {
        // 打开高德地图
        uni.openLocation({
          latitude,
          longitude,
          name,
          success: () => {
            this.closeMapModal()
          }
        })
      } else if (type === 'baidu') {
        // 打开百度地图
        uni.openLocation({
          latitude,
          longitude,
          name,
          success: () => {
            this.closeMapModal()
          }
        })
      }
    },
    haversineDistance(lat1, lon1, lat2, lon2) {
      const R = 6371; // 地球半径(km)
      const dLat = (lat2 - lat1) * Math.PI / 180;
      const dLon = (lon2 - lon1) * Math.PI / 180;
      const a =
        Math.sin(dLat / 2) * Math.sin(dLat / 2) +
        Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
        Math.sin(dLon / 2) * Math.sin(dLon / 2);
      const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
      return R * c;
    },
    // 地图区域变化事件
    onRegionChange(e) {
      return false
      // 防抖处理
      if (this.debounceTimer) {
        clearTimeout(this.debounceTimer);
      }

      this.debounceTimer = setTimeout(() => {
        // 获取地图上下文
        const mapContext = uni.createMapContext('nearbyMap');

        // 获取当前地图视野范围
        mapContext.getRegion({
          success: (res) => {
            const { southwest, northeast } = res;

            // 获取视野中心点
            const centerLat = (southwest.latitude + northeast.latitude) / 2;
            const centerLng = (southwest.longitude + northeast.longitude) / 2;
            let distance = ''
            // 判断方向
            if (centerLat > this.latitude && centerLng < this.longitude) {

              // 计算左上角与当前位置的距离
              distance = this.haversineDistance(
                southwest.latitude,
                southwest.longitude,
                this.formData.latitude,
                this.formData.longitude
              );



            } else if (centerLat > this.latitude && centerLng > this.longitude) {

              // 计算右上角与当前位置的距离
              distance = this.haversineDistance(
                northeast.latitude,
                southwest.longitude,
                this.formData.latitude,
                this.formData.longitude
              );



            } else if (centerLat < this.latitude && centerLng > this.longitude) {

              // 计算右下角与当前位置的距离
              distance = this.haversineDistance(
                southwest.latitude,
                northeast.longitude,
                this.formData.latitude,
                this.formData.longitude
              );



            } else if (centerLat < this.latitude && centerLng < this.longitude) {

              // 计算左下角与当前位置的距离
              distance = this.haversineDistance(
                southwest.latitude,
                southwest.longitude,
                this.formData.latitude,
                this.formData.longitude
              );



            }
            this.formData.distance = parseInt(distance) + 1
            console.log("我懂摄像头了", this.formData.distance);

            // 直接调用获取数据，而不是依赖watch
            this.getLocationListData({ ...this.formData })
          }
        });
      }, 300);
    },
    // 根据缩放级别加载更多点位
    loadMoreLocations(scale) {

      // 计算加载半径，缩放级别越小，半径越大
      const radius = Math.floor(5000 / scale); // 示例计算方式
      // 创建当前区域的标识，避免重复加载
      const areaKey = `${this.latitude.toFixed(3)},${this.longitude.toFixed(3)},${radius}`;


      // 检查是否已经加载过该区域
      if (this.loadedAreas.includes(areaKey)) {
        return;
      }

      // 记录当前加载区域
      this.loadedAreas.push(areaKey);

      // 这里可以调用API获取指定半径内的场地
      // 临时模拟加载更多数据
      uni.showLoading({ title: '加载更多场地...' });

      // 模拟API请求
      setTimeout(() => {
        // 随机生成的运动类型
        const sportTypes = this.selectedSports.map(s => s.sportsName);
        const defaultType = this.currentSport.sportsName || (sportTypes.length > 0 ? sportTypes[0] : '篮球');

        // 模拟新增的场地数据
        const newLocations = [];

        // 为每种选中的运动类型生成场地
        for (let i = 0; i < sportTypes.length; i++) {
          newLocations.push({
            id: this.locations.length + newLocations.length + 1,
            name: `${sportTypes[i]}场地-${Math.floor(Math.random() * 1000)}`,
            description: '根据地图缩放动态加载的场地',
            distance: `${(radius / 1000).toFixed(1)}km`,
            image: '/static/logo.png',
            type: sportTypes[i],
            latitude: this.latitude + (Math.random() - 0.5) * 0.02,
            longitude: this.longitude + (Math.random() - 0.5) * 0.02
          });
        }

        // 添加新场地到列表中
        this.locations = [...this.locations, ...newLocations];

        // 更新地图标记点
        this.updateMarkers();

        uni.hideLoading();

        // 显示加载信息
        uni.showToast({
          title: `已加载${newLocations.length}个新场地`,
          icon: 'none',
          duration: 2000
        });
      }, 500);
    },
    // 刷新场地列表
    refreshLocations() {
      // 显示加载中
      uni.showLoading({
        title: '刷新场地数据...'
      });
      
      // 获取最新位置
      uni.getLocation({
        type: 'gcj02',
        success: (res) => {
          // 更新当前位置
          this.formData.latitude = res.latitude;
          this.formData.longitude = res.longitude;
          this.isReady = true
          // 调用接口获取最新数据
          
          this.getLocationListData({ ...this.formData });
          
          // 移动地图到当前位置
          const mapContext = uni.createMapContext('nearbyMap');
          mapContext.moveToLocation();
          
          // 隐藏加载提示
          uni.hideLoading();
          
          uni.showToast({
            title: '已刷新场地数据',
            icon: 'none'
          });
        },
        fail: () => {
          uni.hideLoading();
          uni.showToast({
            title: '获取位置失败，请检查定位权限',
            icon: 'none'
          });
        }
      });
    },

    // 移动到当前位置
    moveToCurrentLocation() {
      uni.getLocation({
        type: 'gcj02',
        success: (res) => {
          this.latitude = res.latitude;
          this.longitude = res.longitude;

          // 创建地图上下文对象
          const mapContext = uni.createMapContext('nearbyMap');
          // 将地图中心移动到当前位置
          mapContext.moveToLocation();

          uni.showToast({
            title: '已定位到当前位置',
            icon: 'none'
          });
        },
        fail: () => {
          uni.showToast({
            title: '获取位置失败，请检查定位权限',
            icon: 'none'
          });
        }
      });
    },
    closePopup() {
      this.popupShow = false;
    }
  }
}
</script>

<style lang="scss" scoped>
button {
  margin: 0;
}

page {
  overflow: hidden;
}

.container {
  min-height: 100vh;
  background-color: #f5f5f5;
  position: relative;
  overflow: hidden;
}

.nav-bar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 999;
  background: #4CAF50;
}

.header {
  padding: 20rpx 30rpx;
  text-align: center;
  position: relative;

  .title {
    color: #fff;
    font-size: 36rpx;
    font-weight: 500;
  }
}

.page-content {
  padding-top: calc(var(--status-bar-height) + 88rpx);
}

.map-container {
  height: 100vh;
  position: relative;
  width: 750rpx;
  overflow: hidden;

  .map {
    width: 100%;
    height: 100%;
  }
  
  .distance-tip {
    position: absolute;
    right: 20rpx;
    bottom: 40rpx;
    background: rgba(0, 0, 0, 0.6);
    color: #fff;
    padding: 10rpx 20rpx;
    border-radius: 8rpx;
    font-size: 24rpx;
    z-index: 5;
  }

  .map-controls {
    position: absolute;
    right: 30rpx;
    bottom: 220rpx;
    display: flex;
    flex-direction: column;
    gap: 20rpx;
    z-index: 5;

    .control-btn {
      width: 80rpx;
      height: 80rpx;
      background: rgba(255, 255, 255, 0.9);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.2);

      .iconfont {
        font-size: 40rpx;
        color: #333;
      }
    }
  }
}

.filter-container {
  position: fixed;
  top: calc(var(--status-bar-height) + 88rpx);
  left: 0;
  right: 0;
  z-index: 10;
  background: rgba(255, 255, 255, 0.9);
  padding: 10rpx;
  white-space: nowrap;
  border-bottom: 1rpx solid #eee;
  width: 750rpx;
  box-sizing: border-box;
}

.filter-tabs {
  display: flex;
  white-space: nowrap;
  width: 750rpx;
  box-sizing: border-box;

  .filter-tab {
    display: inline-block;
    padding: 14rpx 32rpx;
    margin-right: 20rpx;
    border-radius: 40rpx;
    background: #f0f0f0;
    font-size: 26rpx;
    color: #666;

    &.active {
      background: #4CAF50;
      color: #fff;
    }
  }

  .add-sport-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 70rpx;
    height: 70rpx;
    border-radius: 40rpx;
    background: #f0f0f0;
    margin-left: 20rpx;

    .plus {
      font-size: 40rpx;
      color: #666;
      line-height: 1;
    }
  }
}

.list-container {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  width: 750rpx;
  box-sizing: border-box;
  z-index: 10;
  border-top-left-radius: 24rpx;
  border-top-right-radius: 24rpx;
  box-shadow: 0 -4rpx 10rpx rgba(0, 0, 0, 0.1);
  padding: 0 20rpx;
  max-height: 70vh;
  overflow-y: auto;
}

.close-btn {
  position: absolute;
  top: 10rpx;
  right: 30rpx;
  width: 50rpx;
  height: 50rpx;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.2);
  font-size: 40rpx;
  color: #666;
  font-weight: bold;
  z-index: 11;
}

.location-card {
  background: #fff;
  border-radius: 16rpx;
  margin-bottom: 10rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);

  .location-image {
    width: 100%;
    height: 300rpx;
  }

  .location-image-item {
    width: 100%;
    height: 100%;
  }

  .location-info {
    padding: 20rpx;

    .location-title {
	display: flex;
	flex-direction: row;
	align-items: center;
	justify-content: space-between;
	margin-bottom: 10rpx;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
  .price {
	font-size: 28rpx;
	font-weight: 500;
	color: #FF6B00;
	margin-left: 10rpx;
	flex-shrink: 0;

	&.free {
		color: #4CAF50;
	}
}
}

.location-name {
	font-size: 32rpx;
	font-weight: 500;
	color: #333;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
	flex: 1;
}

    .location-desc {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      font-size: 24rpx;
      color: #666;
      margin-bottom: 8rpx;
    }

    .location-meta {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-top: 20rpx;

      .distance {
        font-size: 24rpx;
        color: #666;
      }

      .navigate-btn {
        margin: 0;
        background: #4CAF50;
        color: #fff;
        font-size: 28rpx;
        display: flex;
        align-items: center;

        .iconfont {
          margin-right: 8rpx;
        }
      }
    }
  }
}

.map-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  width: 750rpx;

  .map-options {
    background: #fff;
    padding: 40rpx;
    border-radius: 24rpx;
    width: 600rpx;

    .map-option {
      padding: 24rpx;
      margin: 16rpx 0;
      border: 1rpx solid #ddd;
      border-radius: 16rpx;
      display: flex;
      align-items: center;

      .iconfont {
        margin-right: 16rpx;
        font-size: 32rpx;
        color: #666;
      }
    }
  }
}

.sport-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;

  .sport-options {
    background: #fff;
    padding: 40rpx;
    border-radius: 24rpx;
    width: 80%;
    max-width: 600rpx;

    .modal-header {
      text-align: center;
      margin-bottom: 40rpx;

      .modal-title {
        font-size: 32rpx;
        font-weight: 500;
        color: #333;
        margin-bottom: 8rpx;
        display: block;
      }

      .modal-subtitle {
        font-size: 24rpx;
        color: #999;
      }
    }

    .sport-grid {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 20rpx;
      margin-bottom: 40rpx;

      .sport-item {
        height: 80rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        background: #f5f5f5;
        border-radius: 12rpx;
        font-size: 28rpx;
        color: #666;

        &.selected {
          background: #4CAF50;
          color: #fff;
        }
      }
    }

    .confirm-btn {
      width: 100%;
      height: 88rpx;
      background: #4CAF50;
      color: #fff;
      font-size: 32rpx;
      border-radius: 44rpx;

      &[disabled] {
        background: #ccc;
      }
    }
  }
}
</style>