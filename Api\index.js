import request from "./requests.js"




// 验证用户信息
export const verify = () => {
	return request.get('/api/auth/verify')
}

// 微信登陆
export const wxLogin = (data) => {
	return request.post('/api/auth/wxLogin', data)
}

// 修改用户头像和昵称
export const updateUserInfo = (data)=>{
	return request.post("/api/auth/updateUserInfo",data)
}

// 获取所有运动
export const getAllSports = () => {
	return request.get('/api/sports/getAllSports')
}

// 添加场地
export const addLocation = (data)=>{
	return request.post('/api/sports/addLocation',data)
}

// 获取场地列表
export const getLocationList = (data)=>{
	return request.get('/api/sports/getLocationList',data)
}

// 搜索场地
export const searchLocations = (data)=>{
	return request.get('/api/sports/searchLocations',data)
}

// 获取用户发布的场地列表
export const getUserLocations = (data)=>{
	return request.get('/api/sports/getUserLocations',data)
}

// 删除场地
export const deleteLocation = (data)=>{
	return request.post('/api/sports/deleteLocation',data)
}
// 获取待审核场地列表
export const getPendingLocations = (data)=>{
	return request.get('/api/sports/getPendingLocations',data)
}
// 审核场地
export const verifyLocation = (data)=>{
	return request.post('/api/sports/reviewLocation',data)
}
// 反馈场地
export const feedbackLocation = (data)=>{
	return request.post('/api/sports/feedback',data)
}

// 约球相关API
// 创建约球活动
export const createSportsEvent = (data) => {
	return request.post('/api/sports/create', data)
}

// 获取约球活动列表
export const getSportsEvents = (data) => {
	return request.get('/api/sports/list', data)
}

// 获取约球活动详情
export const getSportsEventDetail = (data) => {
	return request.get('/api/sports/detail', data)
}

// 加入约球活动
export const joinSportsEvent = (data) => {
	return request.post('/api/sports/join', data)
}

// 退出约球活动
export const quitSportsEvent = (data) => {
	return request.post('/api/sports/quit', data)
}

// 获取用户参与的约球活动
export const getUserEvents = (data) => {
	return request.get('/api/sports/userEvents', data)
}

// 取消约球活动
export const cancelSportsEvent = (data) => {
	return request.post('/api/sports/cancel', data)
}
// 订阅消息
export const subscribe = (data) => {
	return request.post('/api/sports/setSubscription', data)
}

// 获取手机对应图片
export const getPhoneImage = (data) => {
	return request.get('/api/auth/getDyWx?phone='+data)
}

// IM相关API
// 获取IM配置信息
export const getIMConfig = () => {
	return request.get('/api/auth/imConfig')
}

// 获取活动群组信息
export const getEventGroup = (eventId) => {
	return request.get('/api/sports/getEventGroup', { eventId })
}