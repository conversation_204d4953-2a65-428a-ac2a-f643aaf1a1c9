<template>
	<view class="container">
		<!-- 页面头部 -->
		<view class="nav-bar">
			<view class="status-bar"></view>
			<view class="header">
				<view class="back-btn" @tap="goBack">
					<image class="back-icon" src="/static/icons/arrow-left.svg" mode="aspectFit"></image>
				</view>
				<text class="title">隐私政策</text>
				<view class="placeholder"></view>
			</view>
		</view>

		<!-- 页面内容区域 -->
		<view class="page-content">
			<view class="privacy-content">
				<view class="privacy-section">
					<view class="section-title">隐私政策</view>
					<view class="section-content">
						<text>本隐私政策描述了我们如何收集、使用和保护您的个人信息。使用"今天去哪运动"应用即表示您同意本隐私政策的条款。</text>
					</view>
				</view>
				
				<view class="privacy-section">
					<view class="section-title">信息收集</view>
					<view class="section-content">
						<text>我们可能收集以下信息：</text>
						<text>• 个人身份信息（如姓名、电子邮件地址、电话号码）</text>
						<text>• 位置信息（用于显示附近的运动场所）</text>
						<text>• 设备信息（如设备型号、操作系统版本）</text>
						<text>• 使用数据（如您如何使用我们的应用）</text>
					</view>
				</view>
				
				<view class="privacy-section">
					<view class="section-title">信息使用</view>
					<view class="section-content">
						<text>我们使用收集的信息：</text>
						<text>• 提供、维护和改进我们的服务</text>
						<text>• 处理和完成交易</text>
						<text>• 发送相关信息，包括确认、通知和更新</text>
						<text>• 提供客户支持</text>
						<text>• 分析和了解用户如何使用我们的应用</text>
					</view>
				</view>
				
				<view class="privacy-section">
					<view class="section-title">信息保护</view>
					<view class="section-content">
						<text>我们采取适当的数据收集、存储和处理措施以及安全措施，以防止未经授权的访问、更改、披露或销毁您的个人信息。</text>
					</view>
				</view>
				
				<view class="privacy-section">
					<view class="section-title">第三方服务</view>
					<view class="section-content">
						<text>我们的应用可能包含第三方服务的链接。这些第三方网站或服务有其自己的隐私政策，我们对其内容和活动不负责任。</text>
					</view>
				</view>
				
				<view class="privacy-section">
					<view class="section-title">政策更新</view>
					<view class="section-content">
						<text>我们可能会不时更新我们的隐私政策。我们会通过在此页面上发布新的隐私政策来通知您任何更改。</text>
						<text>最后更新日期：2025年7月1日</text>
					</view>
				</view>
				
				<view class="privacy-section">
					<view class="section-title">联系我们</view>
					<view class="section-content">
						<text>如果您对本隐私政策有任何疑问，请联系我们：</text>
						<text>邮箱：<EMAIL></text>
						<text>腾讯：QQ1143220150</text>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				
			}
		},
		methods: {
			// 返回上一页
			goBack() {
				uni.navigateBack();
			}
		}
	}
</script>

<style lang="scss">
	.container {
		min-height: 100vh;
		background-color: #f5f5f5;
	}

	.nav-bar {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		z-index: 999;
		background: #4CAF50;
	}
	
	.status-bar {
		height: var(--status-bar-height);
	}
	
	.header {
		display: flex;
		align-items: center;
		height: 90rpx;
		padding: 0 30rpx;
		
		.back-btn {
			width: 60rpx;
			height: 60rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			
			.back-icon {
				width: 40rpx;
				height: 40rpx;
				filter: brightness(10);
			}
		}
		
		.title {
			flex: 1;
			text-align: center;
			font-size: 36rpx;
			color: #fff;
			font-weight: 500;
		}
		
		.placeholder {
			width: 60rpx;
		}
	}

	.page-content {
		padding-top: calc(var(--status-bar-height) + 90rpx);
		padding-bottom: 30rpx;
	}
	
	.privacy-content {
		padding: 30rpx;
	}
	
	.privacy-section {
		background: #fff;
		border-radius: 16rpx;
		padding: 30rpx;
		margin-bottom: 30rpx;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
		
		.section-title {
			font-size: 32rpx;
			font-weight: 500;
			color: #333;
			margin-bottom: 20rpx;
			position: relative;
			padding-left: 20rpx;
			
			&:before {
				content: '';
				position: absolute;
				left: 0;
				top: 50%;
				transform: translateY(-50%);
				width: 8rpx;
				height: 30rpx;
				background: #4CAF50;
				border-radius: 4rpx;
			}
		}
		
		.section-content {
			font-size: 28rpx;
			color: #666;
			line-height: 1.8;
			
			text {
				display: block;
				margin-bottom: 10rpx;
			}
		}
	}
</style> 