<template>
	<view class="container">
		<!-- 顶部导航栏 -->
		<view class="nav-bar">
			<view class="status-bar"></view>
			<view class="header">
				<view class="back-btn" @tap="goBack">
					<view class="back-icon">
						<view class="back-arrow"></view>
					</view>
				</view>
				<text class="title">我的场地</text>
			</view>
		</view>

		<!-- 页面内容区域 -->
		<view class="page-content">
			<!-- 发布列表 -->
			<scroll-view class="locations" v-if="locations.length > 0" scroll-y="true" lower-threshold="100" @scrolltolower="loadMore"
				refresher-enabled="true" :refresher-triggered="refreshTriggered" @refresherrefresh="onRefresh"
				:style="{ height: scrollViewHeight + 'px' }">
				
				<!-- 列表内容 -->
				<!-- @click="lookDetail(location)" -->
				<view v-for="(location, index) in locations" :key="index" class="location-card" >
					<swiper class="location-image" indicator-dots autoplay circular>
						<swiper-item v-for="(img, imgIndex) in location.images" :key="imgIndex">
							<image :src="img" mode="" class="location-image-item" lazy-load="true" />
						</swiper-item>
						<swiper-item v-if="!location.images || location.images.length === 0">
							<image src="/static/logo.png" mode="aspectFill" class="location-image-item" />
						</swiper-item>
					</swiper>
					<view class="location-info">
						<view class="location-title">
							<text class="location-name">{{ location.name }}</text>
							<text v-if="location.feeType == 'free'" class="price free">免费</text>
							<text v-else-if="location.feeType == 'unknown'" class="price free">未知</text>
							<text v-else-if="location.feeType == 'hourly'" class="price">¥{{ location.hourlyPrice }}/小时</text>
							<text v-else-if="location.feeType == 'perTime'" class="price">¥{{ location.perTimePrice }}/次</text>
						</view>
						<view class="location-desc">{{ location.description }}</view>
						<view class="location-desc">{{ location.address }}</view>
						<view class="location-footer">
							<text>{{ location.toExamine=='-1'?'已拒绝':location.toExamine=='0'?'审核中':location.toExamine=='1'?'已通过':'' }}</text>
							<text class="publish-date">提交于：{{ formatDate(location.dataTime) }}</text>
							<view class="action-btns">
								<button class="action-btn edit-btn" @tap.stop="editLocation(location)">编辑</button>
								<button class="action-btn delete-btn" @tap.stop="confirmDelete(location)">删除</button>
							</view>
						</view>
					</view>
				</view>

				<!-- 加载状态提示 -->
				<view class="loading-status">
					<text v-if="loading">正在加载中...</text>
					<text v-else-if="!hasMore && locations.length > 0">没有更多数据了</text>
					<text v-else-if="locations.length === 0 && !loading">暂无场地记录</text>
				</view>
			</scroll-view>
			
			<!-- 空状态 -->
			<view class="empty-state" v-if="locations.length === 0 && !loading">
				<image src="/static/images/空状态.png" mode="aspectFit" class="empty-image"></image>
				<text class="empty-text">暂无场地记录</text>
				<button class="add-btn" @tap="navigateToAdd">提交场地</button>
			</view>
		</view>
	</view>
</template> 

<script>
import { getUserLocations, deleteLocation } from "@/Api/index.js";
import Mixins from "../../minix/index.js"
export default {
	  mixins:[Mixins],
	data() {
		return {
			refreshTriggered: false,
			loading: true,
			hasMore: true,
			formData: {
				page: 1,
				pageSize: 10
			},
			locations: [],
			scrollViewHeight: 0
		}
	},
	mounted() {
		this.calcScrollViewHeight();
	},
	onLoad() {
		// 页面首次加载
		this.getMyLocations();
	},
	onShow() {
		// 页面显示时重新计算高度
		this.calcScrollViewHeight();
	},
	// 页面触底事件
	onReachBottom() {
		this.loadMore();
	},
	methods: {
		onRefresh() {
			// 重置页码
			this.formData.page = 1
			// 清空已有列表数据
			this.locations = []
			// 设置刷新状态为true
			this.refreshTriggered = true

			// 重新获取数据
			this.getMyLocations(this.formData)
		},
		// 计算scroll-view的高度
		calcScrollViewHeight() {
			const systemInfo = uni.getSystemInfoSync();
			const statusBarHeight = systemInfo.statusBarHeight;
			const headerHeight = 44; // 标题栏高度，单位px
			const windowHeight = systemInfo.windowHeight;
			// 计算滚动区域高度 = 屏幕高度 - 状态栏高度 - 标题栏高度 - 额外边距(20px)
			this.scrollViewHeight = windowHeight - statusBarHeight - headerHeight - 20;
		},

		goBack() {
			uni.switchTab({ url: '/pages/profile/profile' })
		},
		// 格式化日期
		formatDate(timestamp) {
			if (!timestamp) return '未知';
			
			const date = new Date(Number(timestamp));
			const year = date.getFullYear();
			const month = String(date.getMonth() + 1).padStart(2, '0');
			const day = String(date.getDate()).padStart(2, '0');
			 const hours = String(date.getHours()).padStart(2, '0');
			  const minutes = String(date.getMinutes()).padStart(2, '0');
			  const seconds = String(date.getSeconds()).padStart(2, '0');
			 return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
		},
		
		// 获取我发布的场地列表
		getMyLocations() {
			this.loading = true;
			
			// 创建请求参数的副本，防止修改原始对象
			const requestData = {
				...this.formData
			};
			
			
			getUserLocations(requestData).then(res => {
				if (res.data.code !== 200) {
					uni.showToast({
						title: '获取数据失败',
						icon: 'none'
					});
					this.loading = false;
					this.refreshTriggered = false;
					return;
				}
				
				let arr = res.data.data.locations || [];
				console.log('获取到数据条数:', arr.length);
				
				// 处理图片数据
				arr.forEach(ele => {
					// 确保images是数组
					if (typeof ele.images === 'string') {
						try {
							ele.images = JSON.parse(ele.images);
						} catch (e) {
							// 如果解析失败，设为空数组
							ele.images = [];
						}
					}
				});
				
				// 根据是否是刷新或首次加载，决定是追加还是替换数据
				if (this.formData.page === 1) {
					this.locations = arr;
				} else {
					this.locations = [...this.locations, ...arr];
				}
				
				// 判断是否还有更多数据
				this.hasMore = arr.length >= this.formData.pageSize;
				console.log('是否有更多数据:', this.hasMore);
				
				this.loading = false;
				// 确保刷新状态结束
				this.refreshTriggered = false;
			}).catch(err => {
				console.error('获取我的发布列表失败：', err);
				uni.showToast({
					title: '获取数据失败',
					icon: 'none'
				});
				this.loading = false;
				this.refreshTriggered = false;
			});
		},
		
		// 加载更多数据
		loadMore() {
			console.log('loadMore triggered, hasMore:', this.hasMore, 'loading:', this.loading);
			if (!this.hasMore || this.loading) return;
			
			this.formData.page += 1;
			console.log('Loading page:', this.formData.page);
			this.getMyLocations();
		},
		
		// 查看详情
		lookDetail(item) {
			uni.navigateTo({
				url: "/subpages/location-detail/location-detail?item=" + encodeURIComponent(JSON.stringify(item))
			});
		},
		
		// 编辑场地
		editLocation(location) {
			// 复制一份场地数据，避免直接修改原始数据
			const locationData = JSON.parse(JSON.stringify(location));
			
			// 跳转到添加页面，并传递编辑标识和场地数据
			uni.navigateTo({
				url: "/pages/add-location/add-location",
				success: (res) => {
					// 在页面跳转成功后，通过eventChannel传递参数
					res.eventChannel.emit('acceptLocationData', {
						isEdit: true,
						location: locationData
					});
				}
			});
		},
		
		// 确认删除对话框
		confirmDelete(location) {
			uni.showModal({
				title: '确认删除',
				content: '确定要删除该场地吗？删除后无法恢复。',
				success: (res) => {
					if (res.confirm) {
						this.deleteLocation(location);
					}
				}
			});
		},
		
		// 删除场地
		deleteLocation(data) {
			
			uni.showLoading({
				title: '删除中...'
			});
			
			deleteLocation(data).then(res => {
				uni.hideLoading();
				console.log(res);
				if (res.data.code === 200) {
					uni.showToast({
						title: '删除成功',
						icon: 'success'
					});
					
					// 更新列表，移除已删除项
					this.locations = this.locations.filter(item => {
						console.log(item.id , data.id);
						return item.id !== data.id
					});
					
					// 更新用户发布数量
					if (this.$store.state.userinfo && typeof this.$store.state.userinfo.posts === 'number') {
						this.$store.state.userinfo.posts--;
					}
				} else {
					uni.showToast({
						title: res.data.msg || '删除失败',
						icon: 'none'
					});
				}
			}).catch(err => {
				uni.hideLoading();
				uni.showToast({
					title: '删除失败',
					icon: 'none'
				});
			});
		},
		
		// 跳转到添加页面
		navigateToAdd() {
			uni.navigateTo({
				url: "/pages/add-location/add-location"
			});
		},
		

	}
}
</script>

<style lang="scss">
.container {
	min-height: 100vh;
	background-color: #f5f5f5;
}

.nav-bar {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	z-index: 999;
	background: #4CAF50;
}

.header {
	padding: 20rpx 30rpx;
	display: flex;
	align-items: center;
	position: relative;

	.back-btn {
		position: absolute;
		left: 30rpx;
		color: #fff;
		display: flex;
		align-items: center;
		justify-content: center;
		width: 48rpx;
		height: 48rpx;

		.back-icon {
			width: 36rpx;
			height: 36rpx;
			position: relative;

			.back-arrow {
				width: 20rpx;
				height: 20rpx;
				border-left: 4rpx solid #fff;
				border-bottom: 4rpx solid #fff;
				transform: rotate(45deg);
				position: absolute;
				left: 8rpx;
				top: 8rpx;
			}
		}
	}

	.title {
		flex: 1;
		text-align: center;
		color: #fff;
		font-size: 36rpx;
		font-weight: 500;
	}
}

.page-content {
	padding-top: calc(var(--status-bar-height) + 88rpx);
	padding-bottom: 20rpx;
	height: 100vh;
	box-sizing: border-box;
	display: flex;
	flex-direction: column;
}

.locations {
	padding: 20rpx 30rpx;
	flex: 1;
	width: 100%;
	box-sizing: border-box;
}

.location-card {
	background: #fff;
	border-radius: 16rpx;
	margin-bottom: 30rpx;
	overflow: hidden;
	box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
	
	.location-image {
		width: 100%;
		height: 360rpx;
		
		.location-image-item {
			width: 100%;
			height: 100%;
		}
	}
	
	.location-info {
		padding: 24rpx;
		
		.location-title {
			display: flex;
			align-items: center;
			justify-content: space-between;
			margin-bottom: 16rpx;
			
			.location-name {
				font-size: 32rpx;
				font-weight: 500;
				color: #333;
				flex: 1;
				overflow: hidden;
				text-overflow: ellipsis;
				white-space: nowrap;
			}
			
			.price {
				font-size: 28rpx;
				font-weight: 500;
				color: #FF6B00;
				margin-left: 10rpx;
				flex-shrink: 0;
				
				&.free {
					color: #4CAF50;
				}
			}
		}
		
		.location-desc {
			font-size: 28rpx;
			color: #666;
			line-height: 1.5;
			margin-bottom: 12rpx;
			// 多行省略
			display: -webkit-box;
			-webkit-line-clamp: 2;
			-webkit-box-orient: vertical;
			overflow: hidden;
			text-overflow: ellipsis;
		}
		
		.location-footer {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-top: 20rpx;
			padding-top: 16rpx;
			border-top: 1rpx solid #f0f0f0;
			
			.publish-date {
				font-size: 24rpx;
				color: #999;
			}
			
			.action-btns {
				display: flex;
				gap: 20rpx;
				
				.action-btn {
					min-width: 100rpx;
					height: 60rpx;
					line-height: 60rpx;
					font-size: 24rpx;
					border-radius: 30rpx;
					margin: 0;
					padding: 0 30rpx;
					
					&.edit-btn {
						background: #f0f0f0;
						color: #666;
					}
					
					&.delete-btn {
						background: #fff0f0;
						color: #ff4d4f;
					}
				}
			}
		}
	}
}

.loading-status {
	text-align: center;
	padding: 20rpx 0;
	color: #999;
	font-size: 24rpx;
}

.empty-state {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding-top: 160rpx;
	
	.empty-image {
		width: 300rpx;
		height: 300rpx;
		margin-bottom: 30rpx;
	}
	
	.empty-text {
		font-size: 28rpx;
		color: #999;
		margin-bottom: 40rpx;
	}
	
	.add-btn {
		background: #4CAF50;
		color: #fff;
		font-size: 28rpx;
		padding: 16rpx 60rpx;
		border-radius: 40rpx;
	}
}
</style> 