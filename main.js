import App from './App'
import store from "./store/store.js"
// #ifndef VUE3
import Vue from 'vue'
import './uni.promisify.adaptor'
import {
	verify
} from './Api'
Vue.config.productionTip = false
Vue.prototype.$notice = function(title) {
	uni.showToast({
		title: title,
		icon: "none"
	})
}
/**
 * 将角度转换为弧度
 * @param {number} degrees - 角度值
 * @returns {number} 弧度值
 */
function toRadians(degrees) {
	return degrees * (Math.PI / 180);
}

/**
 * 计算两个经纬度点之间的距离，并返回包含数值和适当中文单位的对象
 * (单位为米或公里，超过1000米显示公里)
 * @param {number} lat1 - 第一个点的纬度 (度)
 * @param {number} lon1 - 第一个点的经度 (度)
 * @param {number} lat2 - 第二个点的纬度 (度)
 * @param {number} lon2 - 第二个点的经度 (度)
 * @returns {{value: string, unit: string}} 包含距离数值(字符串)和单位(字符串)的对象
 */
function getDistanceObjectWithUnit(lat1, lon1, lat2, lon2) {
	const R = 6371000; // 地球的平均半径，单位：米
	const dLat = toRadians(lat2 - lat1);
	const dLon = toRadians(lon2 - lon1);

	const a =
		Math.sin(dLat / 2) * Math.sin(dLat / 2) +
		Math.cos(toRadians(lat1)) * Math.cos(toRadians(lat2)) *
		Math.sin(dLon / 2) * Math.sin(dLon / 2);

	const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

	const distanceInMeters = R * c; // 距离，单位：米

	let value;

	// 判断是否超过1000米
	if (distanceInMeters >= 1000) {
		value = (distanceInMeters / 1000).toFixed(2)+"公里";
	} else {
		value = distanceInMeters.toFixed(2)+"米"; // 保留米并保留两位小数
	}

	return value;
}


App.mpType = 'app'
Vue.prototype.$getDistance = getDistanceObjectWithUnit
Vue.prototype.$verify = async function() {
	if(uni.getStorageSync("ydToken")){
		let {
			data
		} = await verify()
		if(data.code==200){
			store.state.userinfo = data.data.user
			uni.setStorageSync('ydToken', data.data.token)
		}else{
			uni.removeStorageSync("ydToken")
		}
	}
	
	
}

// 示例：计算两个假定点之间的距离
// const point1 = { lat: 34.246247, lon: 108.887125 }; // 北京市中心附近
// const point2 = { lat: 34.34991, lon: 108.92729 }; // 上海市中心附近
// console.log("this: " ,this);
// const distanceInMeters = this.$getDistance(point1.lat, point1.lon, point2.lat, point2.lon);

// console.log(distanceInMeters);

const app = new Vue({
	...App,
	store
})
app.$mount()
// #endif

// #ifdef VUE3
import {
	createSSRApp
} from 'vue'
export function createApp() {
	const app = createSSRApp(App)
	return {
		app
	}
}
// #endif