import Vue from 'vue'
import Vuex from 'vuex'

Vue.use(Vuex)
export default new Vuex.Store({
	state: {
		userinfo: {},
		sportsWord:[],
		participants: [] // 存储当前活动的参与者信息
	},

	mutations: {

		SAVAUSERINFO(state, data) {
			state.userinfo = data
		},
		SAVEWORD(state, data) {

			state.sportsWord = data
		},

		// 保存参与者信息
		SAVE_PARTICIPANTS(state, data) {
			state.participants = data
		},

		// 清空参与者信息
		CLEAR_PARTICIPANTS(state) {
			state.participants = []
		},

		// 退出
		EXIT(state, data) {
			if (uni.getStorageSync('ydToken')) {
				uni.removeStorageSync('ydToken')
				state.userinfo = {}
				state.participants = []
				// uni.switchTab({
				// 	url: "/pages/Me/Me"
				// })
			}
		}
	},
	actions: {

	},
	getters: {
		getUserInfo(state){
			return state.userinfo
		},
		getParticipants(state){
			return state.participants
		},
		// 根据用户ID获取参与者头像
		getParticipantAvatar: (state) => (userId) => {
			const participant = state.participants.find(p => p.user_id && p.user_id.toString() === userId.toString())
			return participant ? participant.avatar : '/static/default-avatar.png'
		}
	}
})