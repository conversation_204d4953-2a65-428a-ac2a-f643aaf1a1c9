

module.exports = {
	mounted() {
		// 设置默认的转发参数
		this.share={
			title: "今天去哪运动",
			path: "/pages/index/index",
			// imageUrl: '../../static/logo.png'
		}
		//白名单 
		const urlList = [
		]
		//获取路由信息
		const pages = getCurrentPages()
		//获取当前路由
		let nowPage = pages[pages.length - 1]
		//判断路由包含‘Other’标识的同时不包含有白名单的路由就禁用
		// if (nowPage.route.search('Other') != -1||nowPage.route.search('GQtouxiang') != -1) {
		// 	uni.hideShareMenu()
		// }
	},
	//分享好友
	onShareAppMessage() {
		return this.share
	},
	// #ifdef MP-WEIXIN
	//朋友圈
	onShareTimeline() {
		return this.share
	}
	// #endif
}
