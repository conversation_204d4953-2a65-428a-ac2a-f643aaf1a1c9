<template>
	<view class="container">
		<!-- 顶部导航栏 -->
    <view class="nav-bar">
			<view class="status-bar"></view>
			<view class="header">
				<view class="back-btn" @tap="goBack">
					<image class="back-icon" src="/static/icons/arrow-left.svg" mode="aspectFit"></image>
				</view>
				<text class="title">场地审核</text>
				<view class="placeholder"></view>
			</view>
		</view>

		<!-- 页面内容区域 -->
		<view class="page-content">
			<!-- 运动类型标签 -->
			<scroll-view class="sports-tabs" scroll-x="true" show-scrollbar="false">
				<view v-for="(sport, index) in sports" :key="index" class="sport-tab"
					:class="{ active: currentSport.id === sport.id }" @tap="selectSport(sport)">
					{{ sport.sportsName }}
				</view>
				<view class="add-sport-btn" @tap="sportModal">
					<text class="plus">+</text>
				</view>
			</scroll-view>

			<!-- 地点列表 -->
			<scroll-view :scroll-top="scrollTop" class="locations" scroll-y="true" lower-threshold="100" @scrolltolower="loadMore"
				refresher-enabled="true" :refresher-triggered="refreshTriggered" @refresherrefresh="onRefresh">
				<!-- 骨架屏 -->
				<view v-if="loading && locations.length === 0" class="skeleton">
					<view v-for="i in 3" :key="i" class="skeleton-card">
						<view class="skeleton-image"></view>
						<view class="skeleton-info">
							<view class="skeleton-title"></view>
							<view class="skeleton-line"></view>
							<view class="skeleton-line"></view>
							<view class="skeleton-footer">
								<view class="skeleton-distance"></view>
								<view class="skeleton-btn"></view>
							</view>
						</view>
					</view>
				</view>

				<!-- 实际内容 -->
				<view v-else v-for="(location, index) in locations" :key="index" class="location-card"
					@click="lookDetail(location)">
					<swiper class="location-image" indicator-dots autoplay circular>
						<swiper-item v-for="(img, imgIndex) in location.images" :key="imgIndex">
							<image :src="img" mode="" class="location-image-item" lazy-load="true" />
						</swiper-item>
						<swiper-item v-if="!location.images || location.images.length === 0">
							<image src="/static/logo.png" mode="aspectFill" class="location-image-item" />
						</swiper-item>
					</swiper>
					<view class="location-info">
						<view class="location-title">
							<text class="location-name">{{ location.name }}</text>
							<text v-if="location.feeType == 'free'" class="price free">免费</text>
							<text v-else-if="location.feeType == 'unknown'" class="price free">未知</text>
							<text v-else-if="location.feeType == 'hourly'" class="price">¥{{ location.hourlyPrice }}/小时</text>
							<text v-else-if="location.feeType == 'perTime'" class="price">¥{{ location.perTimePrice }}/次</text>
						</view>
						<view class="location-desc">{{ location.description }}</view>
						<view class="location-desc">{{ location.address }}</view>
						<view class="location-footer">
							<text class="distance">{{ formatDistance(calculateDistance(location.latitude, location.longitude, latitude, longitude)) }}</text>
							<button class="navigate-btn" @tap.stop="showMapOptions(location)">
								<text class="iconfont icon-navigation"></text>
								导航
							</button>
						</view>
					</view>
				</view>

				<!-- 加载状态提示 -->
				<view class="loading-status">
					<text v-if="loading && locations.length > 0">正在加载中...</text>
					<text v-else-if="!hasMore && locations.length > 0">没有更多数据了</text>
					<!-- <text v-else-if="locations.length === 0 && !loading">暂无数据</text> -->
					<view class="empty-guide" v-if="locations.length === 0 && !loading">
						<image src="../../static/images/空状态.png" mode="widthFix" />
						<view class="empty-text">暂无场地数据</view>
					</view>
				</view>
			</scroll-view>
		</view>
	</view>
</template>

<script>
import {getPendingLocations
	
} from "../../Api/index.js";
import {
	debounce
} from '@/utools/index.js'
import Mixins from "../../minix/index.js"
export default {
	mixins: [Mixins],
	data() {
		return {
			scrollTop:0,
			top: 0,
			shareText: true,
			emptyImage: false,
			longitude: "",
			latitude: "",
			formData: {
				page: 1,
				pageSize: 10,
				type: ''
			},
			refreshTriggered: false, // 下拉刷新状态
			loading: true,
			hasMore: true,
			locationsHeight: '',
			loadedIds: [], // 用于跟踪已加载的地点ID
			// 运动类型列表
			sports: [],
			// 用户选择的运动类型
			selectedSports: [],
			// 当前选中的运动类型
			currentSport: {},
			// 是否显示运动选择弹窗
			showSportModal: false,
			// 是否显示地图选择弹窗
			showMapModal: false,
			// 当前选中的地点
			selectedLocation: null,
			// 场地列表数据
			locations: [],
			distanceOptions: ['默认', '1Km内', '2km内', '3Km内', '4Km内', '5Km内', '6Km内', '7Km内', '8Km内', '9Km内', '10Km内',
				'11Km内', '12Km内', '13Km内', '14Km内', '15Km内', '16Km内', '17Km内', '18Km内', '19Km内', '20Km内'
			],
			typeOptions: ['默认', '免费', '未知', '按次', '按小时'],
			isReady: false,
		}
	},

	mounted() {
    this.sports = this.$store.state.sportsWord
    this.currentSport = this.sports[0]
		this.getCurrentLocation()
	},
	watch: {
		currentSport(newval) {
		
			this.loading = true
			this.formData.type = newval.id
			this.locations=[]
			this.formData.page = 1
			this.$nextTick(()=>{
				this.scrollTop = 0
			})
		},
		formData: {
			handler(newval) {
					this.getPendingLocationsData(newval)
			},
			deep: true,
		},
	},
	computed: {

	},
	methods: {
		showMapOptions(location) {
			// this.selectedLocation = location
			// this.showMapModal = true
			uni.openLocation({
				latitude: Number(location.latitude),
				longitude: Number(location.longitude),
				name: location.name,
				address: location.address,
				scale: 14
			});
		},
		goBack() {
			uni.navigateBack()
		},
		formatDistance(meters) {
      if (meters < 1000) {
        return `${Math.round(meters)}米`;
      } else {
        return `${(meters / 1000).toFixed(1)}公里`;
      }
    },
		calculateDistance(lat1, lon1, lat2, lon2) {
      // 地球半径（单位：千米）
      const R = 6371;

      // 将角度转换为弧度
      const dLat = (lat2 - lat1) * Math.PI / 180;
      const dLon = (lon2 - lon1) * Math.PI / 180;

      // 计算Haversine值
      const a =
        Math.sin(dLat / 2) * Math.sin(dLat / 2) +
        Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
        Math.sin(dLon / 2) * Math.sin(dLon / 2);

      // 计算大圆距离
      const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

      // 计算距离（千米）
      const distanceInKm = R * c;

      // 转换为米
      const distanceInMeters = distanceInKm * 1000;

      return distanceInMeters;
    },
		getCurrentLocation() {
			uni.getLocation({
				type: 'gcj02',
				success: (res) => {
					this.latitude = res.latitude
					this.longitude = res.longitude
				}
			})
		},
		areArraysEqualById(arr1, arr2) {

			// 首先检查数组长度是否相同
			if (arr1.length !== arr2.length) {
				return false;
			}

			// 使用Set存储第二个数组的所有id
			const idSet = new Set(arr2.map(item => item.id));

			// 检查第一个数组的每个元素的id是否都存在于Set中
			return arr1.every(item => idSet.has(item.id));
		},
		lookDetail(item) {
			uni.navigateTo({
				url: "/subpages/location-detail/location-detail?item=" + encodeURIComponent(JSON.stringify(item))+'&flag=examine'
			})
		},
		getDistanceMeters(m) {
			if (m < 1000) {
				return Math.round(m) + '米';
			} else {
				return (m / 1000).toFixed(1) + '公里';
			}
		},
		// 下拉刷新
		onRefresh() {
			// 重置页码
			this.formData.page = 1
			// 清空已有列表数据
			this.locations = []
			// 设置刷新状态为true
			this.refreshTriggered = true

			// 重新获取数据
			this.getPendingLocationsData(this.formData)
		},
		// 获取当前位置对应的场地列表
		getPendingLocationsData: debounce(function (data) {

			if (this.formData.type == '') {
				if (this.refreshTriggered) {
					this.refreshTriggered = false // 如果是刷新状态，取消刷新
				}
				return false
			}
		
			// if (this.loading) return

			this.loading = true

			getPendingLocations(data).then(res => {
				let arr = res.data.data.locations
				arr.forEach(ele => {
					// 确保images是数组
					if (typeof ele.images === 'string') {
						try {
							ele.images = JSON.parse(ele.images)
							// 不再限制只保留第一张图片
						} catch (e) {
							// 如果解析失败，设为空数组
							ele.images = []
						}
					}
				});
				this.locations = [...this.locations, ...arr]
				if (this.locations.length == 0) {
					this.emptyImage = true
				} else {
					this.emptyImage = false

				}
				// 判断是否还有更多数据
				this.hasMore = arr.length >= data.pageSize
				this.loading = false
				// 确保刷新状态结束

				this.refreshTriggered = false

			}).catch(() => {
				this.loading = false
				// 确保刷新状态结束

				this.refreshTriggered = false

			})
		}),

		// 加载更多数据
		loadMore() {
			if (!this.hasMore || this.loading) return

			this.formData.page += 1
			this.loading = true
			this.getPendingLocationsData(this.formData)
		},

		// 添加运动场地
		addAddress() {
			if (this.$store.state.userinfo.nickname) {
				uni.navigateTo({
					url: "/subpages/add-location/add-location"
				})
			} else {
				uni.reLaunch({
					url: "/pages/profile/profile?flag=1"
				});

			}

		},
		// 显示运动选择弹窗
		sportModal() {
			this.showSportModal = true
			if (this.sports.length == 0) {
				// this.getAllSportsData()
			}
		},
		// 关闭运动选择弹窗
		closeSportModal() {
			this.showSportModal = false
		},
		// 切换运动选择状态
		toggleSport(sport) {
			const index = this.selectedSports.findIndex(item => item.id == sport.id);
			if (index > -1) {
				this.selectedSports.splice(index, 1)
			} else if (this.selectedSports.length < 5) {
				this.selectedSports.push(sport)
			} else {
				uni.showToast({
					title: '最多选择5项运动',
					icon: 'none'
				})
			}
		},
		// 确认选择的运动
		confirmSports() {
			if (this.selectedSports.length === 0) {
				uni.showToast({
					title: '请至少选择一项运动',
					icon: 'none'
				})
				return
			}
			// 保存选择
			uni.setStorageSync('selectedSports', JSON.stringify(this.selectedSports))
			// 设置当前选中的运动
			this.currentSport = this.selectedSports[0]
			// 关闭弹窗
			this.closeSportModal()
			// 触发全局事件，通知附近页面更新
			uni.$emit('updateSports', this.selectedSports)
			this.locations = []
			this.getPendingLocationsData(this.formData)

		},
		// 选择运动类型
		selectSport(sport) {
			if (this.currentSport.id === sport.id) return
			this.scrollTop=100
			this.currentSport = sport
		},
		// 显示地图选择弹窗
	
		// 关闭地图选择弹窗
		closeMapModal() {
			this.showMapModal = false
			this.selectedLocation = null
		},
		// 导航到指定地点
		onDistanceChange(e) {
			this.formData.selectedDistanceIndex = Number(e.detail.value)
			this.formData.page = 1
			this.locations = []
		},
		onTypeChange(e) {
			this.formData.selectedTypeIndex = Number(e.detail.value)
			this.formData.page = 1
			this.locations = []

		}
	}
}
</script>

<style lang="scss" scoped>

.empty-image {
	position: fixed;
	width: 60%;
	left: 50%;
	transform: translateX(-50%);
	top: 30%;
}

/* 骨架屏样式 */
.skeleton {
	padding: 0;
}

.skeleton-card {
	background: #fff;
	border-radius: 16rpx;
	margin-bottom: 30rpx;
	overflow: hidden;
	box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.skeleton-image {
	width: 100%;
	height: 300rpx;
	background: linear-gradient(90deg, #f2f2f2 25%, #e6e6e6 50%, #f2f2f2 75%);
	background-size: 200% 100%;
	animation: skeleton-loading 1.5s infinite;
}

.skeleton-info {
	padding: 20rpx;
}

.skeleton-title {
	height: 32rpx;
	width: 60%;
	background: linear-gradient(90deg, #f2f2f2 25%, #e6e6e6 50%, #f2f2f2 75%);
	background-size: 200% 100%;
	animation: skeleton-loading 1.5s infinite;
	margin-bottom: 14rpx;
	border-radius: 4rpx;
}

.skeleton-line {
	height: 24rpx;
	width: 80%;
	background: linear-gradient(90deg, #f2f2f2 25%, #e6e6e6 50%, #f2f2f2 75%);
	background-size: 200% 100%;
	animation: skeleton-loading 1.5s infinite;
	margin-bottom: 10rpx;
	border-radius: 4rpx;
}

.skeleton-footer {
	display: flex;
	align-items: center;
	justify-content: space-between;
	margin-top: 20rpx;
}

.skeleton-distance {
	height: 24rpx;
	width: 100rpx;
	background: linear-gradient(90deg, #f2f2f2 25%, #e6e6e6 50%, #f2f2f2 75%);
	background-size: 200% 100%;
	animation: skeleton-loading 1.5s infinite;
	border-radius: 4rpx;
}

.skeleton-btn {
	height: 60rpx;
	width: 120rpx;
	background: linear-gradient(90deg, #f2f2f2 25%, #e6e6e6 50%, #f2f2f2 75%);
	background-size: 200% 100%;
	animation: skeleton-loading 1.5s infinite;
	border-radius: 40rpx;
}

@keyframes skeleton-loading {
	0% {
		background-position: 200% 0;
	}

	100% {
		background-position: -200% 0;
	}
}

.container {
	height: 100vh;
	background-color: #f5f5f5;
	display: flex;
	flex-direction: column;

	.addAddress {
		width: 100rpx;
		height: 100rpx;
		position: fixed;
		bottom: 120rpx;
		right: 20rpx;
		border-radius: 50%;
		background-color: #4CAF50;
		display: flex;
		justify-content: center;
		align-items: center;
		font-size: 34rpx;
		font-weight: bold;
		color: #fff;
	}
}

.nav-bar {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		z-index: 999;
		background: #4CAF50;
	}
	
	.status-bar {
		height: var(--status-bar-height);
	}
	
	.header {
		display: flex;
		align-items: center;
		height: 90rpx;
		padding: 0 30rpx;
		
		.back-btn {
			width: 60rpx;
			height: 60rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			
			.back-icon {
				width: 40rpx;
				height: 40rpx;
				filter: brightness(10);
			}
		}
		
		.title {
			flex: 1;
			text-align: center;
			font-size: 36rpx;
			color: #fff;
			font-weight: 500;
		}
		
		.placeholder {
			width: 60rpx;
		}
	}

.page-content {
	padding-top: calc(var(--status-bar-height) + 88rpx);
	flex: 1;
	overflow: hidden;
}

.filter-bar {
	display: flex;
	align-items: center;
	justify-content: space-around;
	background: #fff;
	padding: 0 10rpx;
	border-bottom: 1rpx solid #eee;
	margin-bottom: 0;
	min-height: 70rpx;

	.filter-item {
		display: inline-flex;
		align-items: center;
		padding: 10rpx 32rpx;
		margin-right: 20rpx;
		border-radius: 40rpx;
		background: #f0f0f0;
		font-size: 26rpx;
		color: #666;
		border: none;
		transition: background 0.2s;
	}

	// picker 选中时高亮
	picker[show] .filter-item,
	.filter-item.active {
		background: #4CAF50;
		color: #fff;
	}
}

.sports-tabs {
	display: flex;
	padding: 10rpx;
	background: #fff;
	white-space: nowrap;
	border-bottom: 1rpx solid #eee;
	align-items: center;

	.sport-tab {
		display: inline-block;
		padding: 14rpx 32rpx;
		margin-right: 20rpx;
		border-radius: 40rpx;
		background: #f0f0f0;
		font-size: 26rpx;
		color: #666;

		&.active {
			background: #4CAF50;
			color: #fff;
		}
	}

	.add-sport-btn {
		display: inline-flex;
		align-items: center;
		justify-content: center;
		width: 70rpx;
		height: 70rpx;
		border-radius: 40rpx;
		background: #f0f0f0;
		margin-left: 20rpx;

		.plus {
			font-size: 40rpx;
			color: #666;
			line-height: 1;
		}
	}
}

.locations {
	padding: 30rpx;
	height: calc(100vh - 270rpx);
	overflow: hidden;
}

.location-desc {
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
	font-size: 24rpx;
	color: #666;
	margin-bottom: 8rpx;
}

.map-modal {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.5);
	display: flex;
	justify-content: center;
	align-items: center;

	.map-options {
		background: #fff;
		padding: 40rpx;
		border-radius: 24rpx;
		width: 80%;
		max-width: 600rpx;

		.map-option {
			padding: 24rpx;
			margin: 16rpx 0;
			border: 1rpx solid #ddd;
			border-radius: 16rpx;
			display: flex;
			align-items: center;

			.iconfont {
				margin-right: 16rpx;
				font-size: 32rpx;
				color: #666;
			}
		}
	}
}

.sport-modal {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.5);
	display: flex;
	justify-content: center;
	align-items: center;
	z-index: 1000;

	.sport-options {
		background: #fff;
		padding: 40rpx;
		border-radius: 24rpx;
		width: 80%;
		max-width: 600rpx;

		.modal-header {
			text-align: center;
			margin-bottom: 40rpx;

			.modal-title {
				font-size: 32rpx;
				font-weight: 500;
				color: #333;
				margin-bottom: 8rpx;
				display: block;
			}

			.modal-subtitle {
				font-size: 24rpx;
				color: #999;
			}
		}

		.sport-grid {
			display: grid;
			grid-template-columns: repeat(3, 1fr);
			gap: 20rpx;
			margin-bottom: 40rpx;

			.sport-item {
				height: 80rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				background: #f5f5f5;
				border-radius: 12rpx;
				font-size: 28rpx;
				color: #666;

				&.selected {
					background: #4CAF50;
					color: #fff;
				}
			}
		}

		.confirm-btn {
			width: 100%;
			height: 88rpx;
			background: #4CAF50;
			color: #fff;
			font-size: 32rpx;
			border-radius: 44rpx;

			&[disabled] {
				background: #ccc;
			}
		}
	}
}

.loading-status {
	text-align: center;
	padding: 20rpx 0;
	color: #999;
	font-size: 24rpx;
}

.location-card {
	background: #fff;
	border-radius: 16rpx;
	margin-bottom: 30rpx;
	overflow: hidden;
	box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.location-image {
	width: 100%;
	height: 300rpx;
}

.location-image-item {
	width: 100%;
	height: 100%;
}

.location-info {
	padding: 20rpx;
}

.location-title {
	display: flex;
	flex-direction: row;
	align-items: center;
	justify-content: space-between;
	margin-bottom: 10rpx;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
}

.location-name {
	font-size: 32rpx;
	font-weight: 500;
	color: #333;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
	flex: 1;
}

.price {
	font-size: 28rpx;
	font-weight: 500;
	color: #FF6B00;
	margin-left: 10rpx;
	flex-shrink: 0;

	&.free {
		color: #4CAF50;
	}
}

.location-footer {
	display: flex;
	align-items: center;
	justify-content: space-between;
	margin-top: 10rpx;
	margin-bottom: 10rpx;

	.distance {
		font-size: 24rpx;
		color: #999;
		text-align: left;
	}

	.navigate-btn {
		margin: 0;
		background: #4CAF50;
		color: #fff;
		// padding: 16rpx 32rpx;
		// border-radius: 40rpx;
		font-size: 28rpx;
		display: flex;
		align-items: center;

		.iconfont {
			margin-right: 8rpx;
		}
	}
}

.location-meta {
	display: flex;
	align-items: center;
	justify-content: space-between;
	margin-top: 20rpx;
}

.distance {
	font-size: 24rpx;
	color: #666;
}



.iconfont {
	margin-right: 6rpx;
}
.empty-guide {
		position: fixed;
		top: 30%;
		left: 50%;
		transform: translate(-50%, -50%);
		// background: rgba(255, 255, 255, 0.8);
		text-align: center;

		.empty-text {
			font-size: 28rpx;
			color: #666;
			margin: 20rpx 0;
			margin-top: -50rpx;
		}

		.publish-btn {
			background: #4CAF50;
			color: #fff;
			width: fit-content;
			padding: 10rpx 32rpx;
			border-radius: 40rpx;
			font-size: 32rpx;
		}
	}
</style>