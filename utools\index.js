/**
 * 压缩图片（支持URL或File对象）
 * @param {string|File} source - 图片URL或File对象
 * @param {Object} options - 压缩选项
 * @param {number} [options.quality=0.8] - 压缩质量（0-1）
 * @param {number} [options.maxWidth] - 最大宽度（可选）
 * @param {number} [options.maxHeight] - 最大高度（可选）
 * @param {boolean} [options.halfSize=true] - 未指定宽高时，是否默认缩小一半
 * @returns {Promise<Blob>} - 返回压缩后的Blob对象
 */
// export function compressImage(tempFilePath, options = {}) {
//   const { quality = 0.8 } = options;

//   return new Promise((resolve, reject) => {
//     wx.getImageInfo({
//       src: tempFilePath,
//       success: (res) => {
//         let { width, height } = res;

//         // 使用离屏Canvas（推荐方案）
//         try {
//           const canvas = wx.createOffscreenCanvas({ type: '2d', width, height });
//           const ctx = canvas.getContext('2d');

//           // 创建图片对象
//           const img = canvas.createImage();
//           img.src = tempFilePath;
//           img.onload = () => {
//             ctx.drawImage(img, 0, 0, width, height);

//             // 导出画布内容
//             wx.canvasToTempFilePath({
//               canvas: canvas,
//               quality: quality,
//               fileType: 'webp',
//               success: (res) => resolve(res.tempFilePath),
//               fail: (err) => reject(new Error('图片压缩失败: ' + err.errMsg))
//             });
//           };
//           img.onerror = () => reject(new Error('图片加载失败'));
//         } catch (e) {
//           // 如果不支持离屏Canvas，回退到原方案（需要确保页面有canvas元素）
//           const ctx = wx.createCanvasContext('compressCanvas');
//           const query = wx.createSelectorQuery();

//           query.select('#compressCanvas')
//             .fields({ node: true, size: true })
//             .exec((res) => {
//               if (!res[0]) return reject(new Error('未找到Canvas元素'));

//               const canvas = res[0];
//               canvas.width = width;
//               canvas.height = height;

//               // 必须等待下一帧再绘制
//               setTimeout(() => {
//                 ctx.drawImage(tempFilePath, 0, 0, width, height);
//                 ctx.draw(true, () => {
//                   wx.canvasToTempFilePath({
//                     canvasId: 'compressCanvas',
//                     quality: quality,
//                     fileType: 'webp',
//                     success: (res) => resolve(res.tempFilePath),
//                     fail: (err) => reject(new Error('导出失败: ' + err.errMsg))
//                   });
//                 });
//               }, 50);
//             });
//         }
//       },
//       fail: (err) => reject(new Error('获取图片信息失败: ' + err.errMsg))
//     });
//   });
// }


// Blob转Base64工具函数
export function tempFilePathToBase64(tempFilePath) {
	return new Promise((resolve, reject) => {
		
		wx.getFileSystemManager().readFile({
			filePath: tempFilePath,
			encoding: 'base64', // 关键：指定返回 Base64
			success: (res) => {
				// 补充 MIME 类型（如 jpg/png）
				const fileType = tempFilePath.split('.').pop();
				const base64 = `data:image/${fileType};base64,${res.data}`;
				resolve(base64);
			},
			fail: (err) => {
				reject(new Error('读取文件失败'));
			},
		});
	});
}

export function compressImage(tempFilePath) {
	return new Promise((resolve, reject) => {
uni.getImageInfo({
			src: tempFilePath,
			success: (res) => {
				let {
					width,
					height
				} = res;
				uni.compressImage({
					src: tempFilePath,
					quality: 40, // 压缩质量，范围0-100
					compressedWidth: width,
					compressedHeight: height,
					success: res => {
						resolve(res.tempFilePath);
					},
					fail: err => {
						reject(err);
					}
				});
			}
			
		})
		
	});
}

// 获取base64编码
export function getBase64(tempFilePath) {
	return new Promise((resolve, reject) => {
		uni.getFileSystemManager().readFile({
			filePath: tempFilePath,
			encoding: 'base64',
			success: res => {
				// 添加适当的前缀（根据图片类型）
				let prefix = 'data:image/jpeg;base64,';
				if (tempFilePath.indexOf('.png') !== -1) {
					prefix = 'data:image/png;base64,';
				} else if (tempFilePath.indexOf('.gif') !== -1) {
					prefix = 'data:image/gif;base64,';
				}
				resolve(prefix + res.data);
			},
			fail: err => {
				reject(err);
			}
		});
	});
}

/**
 * 防抖函数，返回一个防抖后的新函数
 * @param {Function} func - 需要防抖的函数
 * @param {number} wait - 延迟时间（毫秒）
 * @returns {Function} - 防抖后的函数
 */
export function debounce(func, wait = 300) {
  let timeout;
  return function (...args) {
    const context = this;
    clearTimeout(timeout);
    timeout = setTimeout(() => {
      func.apply(context, args);
    }, wait);
  };
}

