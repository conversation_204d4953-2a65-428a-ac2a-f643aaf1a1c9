<template>
	<view class="container">
		<!-- 顶部导航栏 -->
		<view class="nav-bar">
			<view class="status-bar"></view>
			<view class="header">
				<view class="back-btn" @tap="goBack">
					<view class="back-icon">
						<view class="back-arrow"></view>
					</view>
				</view>
				<text class="title">意见反馈</text>
				<view class="submit-btn" @tap="submitFeedback">
					<text class="submit-text">提交</text>
				</view>
			</view>
		</view>

		<!-- 页面内容区域 -->
		<view class="page-content">
			<!-- 反馈类型 -->
			<view class="form-section">
				<view class="section-title">反馈类型</view>
				<view class="type-list">
					<view v-for="(type, index) in feedbackTypes" :key="index" 
						  class="type-item" 
						  :class="{ active: selectedType === type.value }"
						  @tap="selectType(type.value)">
						<text class="type-text">{{ type.label }}</text>
					</view>
				</view>
			</view>

			<!-- 问题描述 -->
			<view class="form-section">
				<view class="section-title">问题描述 <text class="required">*</text></view>
				<view class="textarea-wrapper">
					<textarea 
						class="feedback-textarea" 
						placeholder="请详细描述您遇到的问题或建议，我们会认真对待每一条反馈"
						v-model="feedbackContent"
						maxlength="500"
						:show-confirm-bar="false">
					</textarea>
					<view class="char-count">{{ feedbackContent.length }}/500</view>
				</view>
			</view>

			<!-- 联系方式 -->
			<view class="form-section">
				<view class="section-title">联系方式</view>
				<view class="input-wrapper">
					<input 
						class="contact-input" 
						placeholder="请留下您的联系方式，方便我们回复（选填）"
						v-model="contactInfo"
						maxlength="50">
				</view>
			</view>

			<!-- 图片上传 -->
			<view class="form-section">
				<view class="section-title">相关截图</view>
				<view class="upload-section">
					<view class="image-list">
						<view v-for="(image, index) in uploadedImages" :key="index" class="image-item">
							<image :src="image" mode="aspectFill" class="uploaded-image"></image>
							<view class="delete-btn" @tap="deleteImage(index)">
								<text class="delete-icon">×</text>
							</view>
						</view>
						<view v-if="uploadedImages.length < 3" class="upload-btn" @tap="chooseImage">
							<view class="upload-icon">
								<text class="plus-icon">+</text>
							</view>
							<text class="upload-text">添加图片</text>
						</view>
					</view>
					<view class="upload-tip">最多可上传3张图片，支持jpg、png格式</view>
				</view>
			</view>

			<!-- 历史反馈 -->
			<view class="form-section">
				<view class="section-title">我的反馈记录</view>
				<view class="history-list">
					<view v-for="(item, index) in feedbackHistory" :key="index" class="history-item" @tap="viewFeedback(item)">
						<view class="history-content">
							<view class="history-type">{{ getTypeLabel(item.type) }}</view>
							<view class="history-desc">{{ item.content }}</view>
							<view class="history-time">{{ formatTime(item.createTime) }}</view>
						</view>
						<view class="history-status" :class="item.status">
							<text class="status-text">{{ getStatusText(item.status) }}</text>
						</view>
					</view>
					<view v-if="feedbackHistory.length === 0" class="empty-state">
						<text class="empty-text">暂无反馈记录</text>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import Mixins from "../../minix/index.js"

export default {
	mixins: [Mixins],
	data() {
		return {
			selectedType: 'bug',
			feedbackContent: '',
			contactInfo: '',
			uploadedImages: [],
			feedbackTypes: [
				{ label: '功能异常', value: 'bug' },
				{ label: '功能建议', value: 'suggestion' },
				{ label: '内容问题', value: 'content' },
				{ label: '其他问题', value: 'other' }
			],
			feedbackHistory: [
				{
					id: 1,
					type: 'bug',
					content: '地图定位不准确，显示的位置与实际位置相差很远',
					status: 'resolved',
					createTime: '2024-01-15 14:30:00',
					reply: '感谢您的反馈，我们已经优化了定位算法，请更新到最新版本体验。'
				},
				{
					id: 2,
					type: 'suggestion',
					content: '希望能增加夜间模式，保护眼睛',
					status: 'processing',
					createTime: '2024-01-10 09:15:00'
				}
			]
		}
	},
	
	mounted() {
		this.initNavBar()
		this.loadFeedbackHistory()
	},
	
	methods: {
		// 初始化导航栏
		initNavBar() {
			this.$nextTick(() => {
				const res = uni.getMenuButtonBoundingClientRect()
				const statusHeight = res.top
				const navHeight = res.height
				this.top = statusHeight + navHeight + 10
			})
		},
		
		// 返回上一页
		goBack() {
			uni.navigateBack()
		},
		
		// 选择反馈类型
		selectType(type) {
			this.selectedType = type
		},
		
		// 选择图片
		chooseImage() {
			uni.chooseImage({
				count: 3 - this.uploadedImages.length,
				sizeType: ['compressed'],
				sourceType: ['album', 'camera'],
				success: (res) => {
					this.uploadedImages = this.uploadedImages.concat(res.tempFilePaths)
				}
			})
		},
		
		// 删除图片
		deleteImage(index) {
			this.uploadedImages.splice(index, 1)
		},
		
		// 提交反馈
		submitFeedback() {
			if (!this.feedbackContent.trim()) {
				uni.showToast({
					title: '请填写问题描述',
					icon: 'none'
				})
				return
			}
			
			uni.showLoading({
				title: '提交中...'
			})
			
			// 模拟提交
			setTimeout(() => {
				uni.hideLoading()
				uni.showToast({
					title: '提交成功',
					icon: 'success'
				})
				
				// 清空表单
				this.feedbackContent = ''
				this.contactInfo = ''
				this.uploadedImages = []
				this.selectedType = 'bug'
				
				// 刷新历史记录
				this.loadFeedbackHistory()
			}, 2000)
		},
		
		// 加载反馈历史
		loadFeedbackHistory() {
			// 这里应该调用API获取用户的反馈历史
			// 暂时使用模拟数据
		},
		
		// 查看反馈详情
		viewFeedback(item) {
			uni.showModal({
				title: this.getTypeLabel(item.type),
				content: item.reply || '我们已收到您的反馈，正在处理中...',
				showCancel: false
			})
		},
		
		// 获取类型标签
		getTypeLabel(type) {
			const typeObj = this.feedbackTypes.find(t => t.value === type)
			return typeObj ? typeObj.label : '其他'
		},
		
		// 获取状态文本
		getStatusText(status) {
			const statusMap = {
				'pending': '待处理',
				'processing': '处理中',
				'resolved': '已解决',
				'closed': '已关闭'
			}
			return statusMap[status] || '未知'
		},
		
		// 格式化时间
		formatTime(timeStr) {
			const date = new Date(timeStr)
			const now = new Date()
			const diff = now - date
			
			if (diff < 60000) { // 1分钟内
				return '刚刚'
			} else if (diff < 3600000) { // 1小时内
				return Math.floor(diff / 60000) + '分钟前'
			} else if (diff < 86400000) { // 1天内
				return Math.floor(diff / 3600000) + '小时前'
			} else {
				return timeStr.split(' ')[0]
			}
		}
	}
}
</script>

<style lang="scss" scoped>
.container {
	height: 100vh;
	background-color: #f5f5f5;
	display: flex;
	flex-direction: column;
}

.nav-bar {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	z-index: 999;
	background: #4CAF50;
}

.header {
	padding: 20rpx 30rpx;
	display: flex;
	align-items: center;
	justify-content: space-between;

	.back-btn {
		width: 60rpx;
		height: 60rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.back-icon {
		width: 24rpx;
		height: 24rpx;
		position: relative;
	}

	.back-arrow {
		width: 16rpx;
		height: 16rpx;
		border-left: 3rpx solid #fff;
		border-bottom: 3rpx solid #fff;
		transform: rotate(45deg);
	}

	.title {
		color: #fff;
		font-size: 36rpx;
		font-weight: 500;
		flex: 1;
		text-align: center;
	}

	.submit-btn {
		padding: 12rpx 24rpx;
		background: rgba(255, 255, 255, 0.2);
		border-radius: 20rpx;
	}

	.submit-text {
		color: #fff;
		font-size: 28rpx;
	}
}

.page-content {
	padding-top: calc(var(--status-bar-height) + 88rpx);
	flex: 1;
	padding: calc(var(--status-bar-height) + 88rpx) 30rpx 30rpx;
}

.form-section {
	margin-bottom: 40rpx;
}

.section-title {
	font-size: 32rpx;
	font-weight: 500;
	color: #333;
	margin-bottom: 20rpx;
	padding-left: 10rpx;
}

.required {
	color: #ff4757;
}

.type-list {
	display: flex;
	flex-wrap: wrap;
	gap: 20rpx;
}

.type-item {
	padding: 20rpx 30rpx;
	background: #fff;
	border-radius: 50rpx;
	border: 2rpx solid #e0e0e0;
	
	&.active {
		background: #4CAF50;
		border-color: #4CAF50;
		
		.type-text {
			color: #fff;
		}
	}
}

.type-text {
	font-size: 28rpx;
	color: #333;
}

.textarea-wrapper {
	background: #fff;
	border-radius: 16rpx;
	padding: 30rpx;
	position: relative;
	box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.feedback-textarea {
	width: 100%;
	min-height: 200rpx;
	font-size: 28rpx;
	color: #333;
	line-height: 1.6;
}

.char-count {
	position: absolute;
	bottom: 20rpx;
	right: 30rpx;
	font-size: 24rpx;
	color: #999;
}

.input-wrapper {
	background: #fff;
	border-radius: 16rpx;
	padding: 30rpx;
	box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.contact-input {
	width: 100%;
	font-size: 28rpx;
	color: #333;
}

.upload-section {
	background: #fff;
	border-radius: 16rpx;
	padding: 30rpx;
	box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.image-list {
	display: flex;
	flex-wrap: wrap;
	gap: 20rpx;
	margin-bottom: 20rpx;
}

.image-item {
	position: relative;
	width: 160rpx;
	height: 160rpx;
}

.uploaded-image {
	width: 100%;
	height: 100%;
	border-radius: 12rpx;
}

.delete-btn {
	position: absolute;
	top: -10rpx;
	right: -10rpx;
	width: 40rpx;
	height: 40rpx;
	background: #ff4757;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
}

.delete-icon {
	color: #fff;
	font-size: 24rpx;
	font-weight: bold;
}

.upload-btn {
	width: 160rpx;
	height: 160rpx;
	border: 2rpx dashed #ddd;
	border-radius: 12rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
}

.upload-icon {
	width: 60rpx;
	height: 60rpx;
	background: #f0f0f0;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-bottom: 10rpx;
}

.plus-icon {
	font-size: 32rpx;
	color: #999;
}

.upload-text {
	font-size: 24rpx;
	color: #999;
}

.upload-tip {
	font-size: 24rpx;
	color: #999;
	text-align: center;
}

.history-list {
	background: #fff;
	border-radius: 16rpx;
	overflow: hidden;
	box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.history-item {
	padding: 30rpx;
	display: flex;
	align-items: flex-start;
	border-bottom: 1rpx solid #f0f0f0;
	
	&:last-child {
		border-bottom: none;
	}
}

.history-content {
	flex: 1;
}

.history-type {
	font-size: 24rpx;
	color: #4CAF50;
	margin-bottom: 8rpx;
}

.history-desc {
	font-size: 28rpx;
	color: #333;
	margin-bottom: 8rpx;
	line-height: 1.4;
}

.history-time {
	font-size: 24rpx;
	color: #999;
}

.history-status {
	padding: 8rpx 16rpx;
	border-radius: 20rpx;
	margin-left: 20rpx;
	
	&.pending {
		background: #fff3cd;
		color: #856404;
	}
	
	&.processing {
		background: #cce5ff;
		color: #004085;
	}
	
	&.resolved {
		background: #d4edda;
		color: #155724;
	}
	
	&.closed {
		background: #f8d7da;
		color: #721c24;
	}
}

.status-text {
	font-size: 24rpx;
}

.empty-state {
	padding: 80rpx 30rpx;
	text-align: center;
}

.empty-text {
	font-size: 28rpx;
	color: #999;
}

/* 状态栏适配 */
.status-bar {
	height: var(--status-bar-height);
	width: 100%;
}
</style>
