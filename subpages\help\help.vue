<template>
	<view class="container">
		<!-- 顶部导航栏 -->
		<view class="nav-bar">
			<view class="status-bar"></view>
			<view class="header">
				<view class="back-btn" @tap="goBack">
					<view class="back-icon">
						<view class="back-arrow"></view>
					</view>
				</view>
				<text class="title">帮助中心</text>
			</view>
		</view>

		<!-- 页面内容区域 -->
		<view class="page-content">
			<!-- 搜索框 -->
			<view class="search-section">
				<view class="search-box">
					<image class="search-icon" src="/static/icons/search.svg" mode="aspectFit"></image>
					<input class="search-input" placeholder="搜索帮助内容" v-model="searchKeyword" @input="onSearch" />
				</view>
			</view>

			<!-- 常见问题 -->
			<view class="help-section">
				<view class="section-title">常见问题</view>
				<view class="help-list">
					<view v-for="(item, index) in filteredFaqList" :key="index" class="help-item" @tap="toggleExpand(index)">
						<view class="help-question">
							<text class="question-text">{{ item.question }}</text>
							<view class="expand-icon" :class="{ expanded: item.expanded }">
								<text>></text>
							</view>
						</view>
						<view class="help-answer" v-if="item.expanded">
							<text class="answer-text">{{ item.answer }}</text>
						</view>
					</view>
				</view>
			</view>

			<!-- 使用指南 -->
			<view class="help-section">
				<view class="section-title">使用指南</view>
				<view class="guide-list">
					<view v-for="(guide, index) in guideList" :key="index" class="guide-item" @tap="viewGuide(guide)">
						<view class="guide-icon">
							<image :src="guide.icon" mode="aspectFit"></image>
						</view>
						<view class="guide-content">
							<text class="guide-title">{{ guide.title }}</text>
							<text class="guide-desc">{{ guide.description }}</text>
						</view>
						<view class="arrow-icon">
							<image src="/static/icons/arrow-right.svg" mode="aspectFit"></image>
						</view>
					</view>
				</view>
			</view>

			<!-- 联系我们 -->
			<view class="help-section">
				<view class="section-title">联系我们</view>
				<view class="contact-list">
					<view class="contact-item" @tap="contactUs('qq')">
						<view class="contact-icon">
							<text class="icon-text">QQ</text>
						</view>
						<view class="contact-content">
							<text class="contact-title">QQ客服</text>
							<text class="contact-desc">1143220150</text>
						</view>
						<view class="arrow-icon">
							<image src="/static/icons/arrow-right.svg" mode="aspectFit"></image>
						</view>
					</view>
					
					<view class="contact-item" @tap="contactUs('email')">
						<view class="contact-icon">
							<text class="icon-text">📧</text>
						</view>
						<view class="contact-content">
							<text class="contact-title">邮箱反馈</text>
							<text class="contact-desc"><EMAIL></text>
						</view>
						<view class="arrow-icon">
							<image src="/static/icons/arrow-right.svg" mode="aspectFit"></image>
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import Mixins from "../../minix/index.js"

export default {
	mixins: [Mixins],
	data() {
		return {
			searchKeyword: '',
			faqList: [
				{
					question: '如何发布运动场地信息？',
					answer: '点击首页右下角的"+"按钮，填写场地信息包括名称、地址、运动类型、收费标准等，提交审核后即可发布。',
					expanded: false
				},
				{
					question: '如何搜索附近的运动场地？',
					answer: '在首页可以看到附近的场地列表，也可以使用搜索功能查找特定的场地，支持按距离、类型、价格等条件筛选。',
					expanded: false
				},
				{
					question: '场地信息不准确怎么办？',
					answer: '如果发现场地信息有误，可以在场地详情页点击"反馈"按钮，或通过意见反馈功能告知我们，我们会及时核实并更新。',
					expanded: false
				},
				{
					question: '如何约球找球友？',
					answer: '进入"约球"页面，可以发布约球信息或查看其他人的约球邀请，选择合适的时间和场地一起运动。',
					expanded: false
				},
				{
					question: '为什么定位不准确？',
					answer: '请确保已开启位置权限，在网络良好的环境下使用。如果仍有问题，可以手动选择位置或联系客服。',
					expanded: false
				}
			],
			guideList: [
				{
					title: '新手入门',
					description: '了解应用基本功能和使用方法',
					icon: '/static/icons/user.svg'
				},
				{
					title: '发布场地',
					description: '学习如何发布和管理场地信息',
					icon: '/static/icons/add.svg'
				},
				{
					title: '约球功能',
					description: '掌握约球和社交功能的使用',
					icon: '/static/icons/heart.svg'
				}
			]
		}
	},
	
	computed: {
		filteredFaqList() {
			if (!this.searchKeyword) {
				return this.faqList
			}
			return this.faqList.filter(item => 
				item.question.includes(this.searchKeyword) || 
				item.answer.includes(this.searchKeyword)
			)
		}
	},
	
	mounted() {
		this.initNavBar()
	},
	
	methods: {
		// 初始化导航栏
		initNavBar() {
			this.$nextTick(() => {
				const res = uni.getMenuButtonBoundingClientRect()
				const statusHeight = res.top
				const navHeight = res.height
				this.top = statusHeight + navHeight + 10
			})
		},
		
		// 返回上一页
		goBack() {
			uni.navigateBack()
		},
		
		// 搜索
		onSearch() {
			// 搜索逻辑已在计算属性中实现
		},
		
		// 展开/收起FAQ
		toggleExpand(index) {
			this.faqList[index].expanded = !this.faqList[index].expanded
		},
		
		// 查看使用指南
		viewGuide(guide) {
			uni.showToast({
				title: '功能开发中',
				icon: 'none'
			})
		},
		
		// 联系我们
		contactUs(type) {
			if (type === 'qq') {
				// #ifdef APP-PLUS
				plus.runtime.openURL('mqqwpa://im/chat?chat_type=wpa&uin=1143220150')
				// #endif
				
				// #ifdef H5 || MP-WEIXIN
				uni.setClipboardData({
					data: '1143220150',
					success: () => {
						uni.showToast({
							title: 'QQ号已复制',
							icon: 'success'
						})
					}
				})
				// #endif
			} else if (type === 'email') {
				uni.setClipboardData({
					data: '<EMAIL>',
					success: () => {
						uni.showToast({
							title: '邮箱已复制',
							icon: 'success'
						})
					}
				})
			}
		}
	}
}
</script>

<style lang="scss" scoped>
.container {
	height: 100vh;
	background-color: #f5f5f5;
	display: flex;
	flex-direction: column;
}

.nav-bar {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	z-index: 999;
	background: #4CAF50;
}

.header {
	padding: 20rpx 30rpx;
	display: flex;
	align-items: center;
	position: relative;

	.back-btn {
		width: 60rpx;
		height: 60rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-right: 20rpx;
	}

	.back-icon {
		width: 24rpx;
		height: 24rpx;
		position: relative;
	}

	.back-arrow {
		width: 16rpx;
		height: 16rpx;
		border-left: 3rpx solid #fff;
		border-bottom: 3rpx solid #fff;
		transform: rotate(45deg);
	}

	.title {
		color: #fff;
		font-size: 36rpx;
		font-weight: 500;
		flex: 1;
		text-align: center;
		margin-right: 80rpx;
	}
}

.page-content {
	padding-top: calc(var(--status-bar-height) + 88rpx);
	flex: 1;
	padding: calc(var(--status-bar-height) + 88rpx) 30rpx 30rpx;
}

.search-section {
	margin-bottom: 30rpx;
}

.search-box {
	background: #fff;
	border-radius: 50rpx;
	padding: 20rpx 30rpx;
	display: flex;
	align-items: center;
	box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.search-icon {
	width: 32rpx;
	height: 32rpx;
	margin-right: 20rpx;
}

.search-input {
	flex: 1;
	font-size: 28rpx;
	color: #333;
}

.help-section {
	margin-bottom: 40rpx;
}

.section-title {
	font-size: 32rpx;
	font-weight: 500;
	color: #333;
	margin-bottom: 20rpx;
	padding-left: 10rpx;
}

.help-list {
	background: #fff;
	border-radius: 16rpx;
	overflow: hidden;
	box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.help-item {
	border-bottom: 1rpx solid #f0f0f0;
	
	&:last-child {
		border-bottom: none;
	}
}

.help-question {
	padding: 30rpx;
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.question-text {
	font-size: 28rpx;
	color: #333;
	flex: 1;
}

.expand-icon {
	width: 24rpx;
	height: 24rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	transition: transform 0.3s;
	
	&.expanded {
		transform: rotate(90deg);
	}
	
	text {
		font-size: 20rpx;
		color: #999;
	}
}

.help-answer {
	padding: 0 30rpx 30rpx;
	background: #f8f9fa;
}

.answer-text {
	font-size: 26rpx;
	color: #666;
	line-height: 1.6;
}

.guide-list, .contact-list {
	background: #fff;
	border-radius: 16rpx;
	overflow: hidden;
	box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.guide-item, .contact-item {
	padding: 30rpx;
	display: flex;
	align-items: center;
	border-bottom: 1rpx solid #f0f0f0;
	
	&:last-child {
		border-bottom: none;
	}
}

.guide-icon, .contact-icon {
	width: 80rpx;
	height: 80rpx;
	background: #f8f9fa;
	border-radius: 16rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 24rpx;
	
	image {
		width: 40rpx;
		height: 40rpx;
	}
	
	.icon-text {
		font-size: 24rpx;
		color: #4CAF50;
		font-weight: bold;
	}
}

.guide-content, .contact-content {
	flex: 1;
}

.guide-title, .contact-title {
	font-size: 30rpx;
	color: #333;
	font-weight: 500;
	display: block;
	margin-bottom: 8rpx;
}

.guide-desc, .contact-desc {
	font-size: 24rpx;
	color: #666;
}

.arrow-icon {
	width: 24rpx;
	height: 24rpx;
}

/* 状态栏适配 */
.status-bar {
	height: var(--status-bar-height);
	width: 100%;
}
</style>
