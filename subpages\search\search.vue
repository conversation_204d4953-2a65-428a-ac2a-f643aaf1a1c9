<template>
	<view class="search-container">
		<!-- 状态栏占位 -->
		<view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>
		
		<!-- 搜索头部 -->
		<view class="search-header" :style="{ paddingRight: headerRightPadding + 'px' }">
			<view class="search-box">
				<image class="search-icon" src="/static/icons/search.svg" mode="aspectFit"></image>
				<input 
					class="search-input" 
					type="text" 
					v-model="searchKeyword" 
					placeholder="搜索场地名称、场地地址..." 
					confirm-type="search"
					@confirm="handleSearch"
					focus
				/>
				<text class="clear-icon" v-if="searchKeyword" @tap="clearSearch">×</text>
			</view>
			<!-- <text class="cancel-btn" @tap="goBack">取消</text> -->
		</view>
		
		<!-- 搜索历史 -->
		<view class="search-history" v-if="!searchKeyword && searchHistory.length > 0">
			<view class="history-header">
				<text class="history-title">搜索历史</text>
				<text class="clear-history" @tap="clearHistory">清除</text>
			</view>
			<view class="history-list">
				<view 
					class="history-item" 
					v-for="(item, index) in searchHistory" 
					:key="index"
					@tap="useHistoryItem(item)"
				>
					<text class="history-text">{{item}}</text>
					<text class="delete-icon" @tap.stop="deleteHistoryItem(index)">×</text>
				</view>
			</view>
		</view>
		
		<!-- 搜索结果 -->
		<scroll-view 
			class="search-results" 
			v-if="searchKeyword && !loading"
			scroll-y="true"
			@scrolltolower="loadMore"
      lower-threshold="100"
      :show-scrollbar="false"
		>
			<view v-if="searchResults.length > 0">
				<view 
					class="result-item" 
					v-for="(item, index) in searchResults" 
					:key="index"
					@tap="goToDetail(item)"
				>
					<image class="result-image" :src="JSON.parse(item.images)[0] || '/static/images/default-location.png'" mode="aspectFill"></image>
					<view class="result-info">
						<view class="result-name">{{item.name}}</view>
						<view class="result-address">{{item.address}}</view>
						<view class="result-tags">
							<text class="tag" v-for="(tag, tagIndex) in item.tags" :key="tagIndex">{{tag}}</text>
						</view>
					</view>
				</view>
				
				<!-- 底部加载状态 -->
				<view class="load-more">
					<text v-if="hasMore">上拉加载更多</text>
					<text v-else>没有更多了</text>
				</view>
			</view>
			<view class="no-results" v-else>
				<image class="no-results-image" src="/static/images/空状态.png" mode="aspectFit"></image>
				<text class="no-results-text">未找到相关结果</text>
			</view>
		</scroll-view>
		
		<!-- 加载中 -->
		<view class="loading" v-if="loading">
			<view class="spinner"></view>
			<text class="loading-text">正在搜索...</text>
		</view>
	</view>
</template>

<script>
import { searchLocations } from '@/Api/index.js'
import Mixins from "../../minix/index.js"

export default {
	mixins: [Mixins],
	data() {
		return {
			searchKeyword: '',
			searchHistory: [],
			searchResults: [],
			loading: false,
			timer: null,
			// 分页相关
			currentPage: 1,
			pageSize: 10,
			totalPages: 0,
			hasMore: false,
			// 状态栏和胶囊按钮区域
			statusBarHeight: 0,
			menuButtonInfo: null,
			headerRightPadding: 0
		}
	},
	onLoad() {
		// 加载搜索历史
		try {
			const history = uni.getStorageSync('searchHistory');
			if (history) {
				this.searchHistory = JSON.parse(history);
			}
		} catch (e) {
			console.error('Failed to load search history:', e);
		}
		
		// 获取状态栏高度和胶囊按钮信息
		try {
			const systemInfo = uni.getSystemInfoSync();
			this.statusBarHeight = systemInfo.statusBarHeight || 20;
			
			// 获取胶囊按钮位置信息
			this.menuButtonInfo = uni.getMenuButtonBoundingClientRect();
			
			// 计算右侧需要留出的安全距离
			if (this.menuButtonInfo) {
				// 计算右侧胶囊按钮左侧到屏幕右侧的距离
				this.headerRightPadding = systemInfo.windowWidth - this.menuButtonInfo.left + 10; // 额外10px间距
			} else {
				this.headerRightPadding = 90; // 默认值
			}
		} catch (e) {
			console.error('Failed to get system info:', e);
			this.statusBarHeight = 20; // 默认值
			this.headerRightPadding = 90; // 默认值
		}
	},
	// 使用scroll-view的scrolltolower事件替代onReachBottom
	// onReachBottom() {
	// 	if (this.searchKeyword && this.hasMore && !this.loading) {
	// 		this.loadMore();
	// 	}
	// },
	methods: {
		// 处理搜索
		handleSearch() {
			if (!this.searchKeyword.trim()) return;
			
			// 重置分页
			this.currentPage = 1;
			
			// 保存到搜索历史
			this.saveToHistory(this.searchKeyword);
			
			// 执行搜索
			this.performSearch();
		},
		
		// 执行搜索请求
		async performSearch() {
			this.loading = true;
			
			try {
				// 实际API调用
				const {data} = await searchLocations({
				    keyword: this.searchKeyword,
				    page: this.currentPage,
				    pageSize: this.pageSize
				});
        
				this.searchResults = data.data.locations;
				this.totalPages = Math.ceil(data.data.pagination.total / this.pageSize)
				this.hasMore = this.currentPage < this.totalPages;
		
				
			} catch (error) {
				console.error('搜索失败:', error);
				uni.showToast({
					title: '搜索失败，请重试',
					icon: 'none'
				});
			} finally {
				this.loading = false;
			}
		},
		

		
		// 加载更多
		async loadMore() {
			if (this.hasMore && !this.loading) {
				this.currentPage++;
				// this.loading = true;
				
				try {
					// 实际API调用
					const {data} = await searchLocations({
					    keyword: this.searchKeyword,
					    page: this.currentPage,
					    pageSize: this.pageSize
					});
					this.searchResults = [...this.searchResults, ...data.data.locations];
					this.totalPages = Math.ceil(data.data.pagination.total / this.pageSize);
					this.hasMore = this.currentPage < this.totalPages;
					
				} catch (error) {
					console.error('加载更多失败:', error);
					uni.showToast({
						title: '加载失败，请重试',
						icon: 'none'
					});
				} finally {
					// this.loading = false;
				}
			}
		},
		
		// 保存到搜索历史
		saveToHistory(keyword) {
			// 移除已存在的相同关键词
			const index = this.searchHistory.indexOf(keyword);
			if (index !== -1) {
				this.searchHistory.splice(index, 1);
			}
			
			// 添加到历史记录开头
			this.searchHistory.unshift(keyword);
			
			// 限制历史记录数量
			if (this.searchHistory.length > 10) {
				this.searchHistory = this.searchHistory.slice(0, 10);
			}
			
			// 保存到本地存储
			try {
				uni.setStorageSync('searchHistory', JSON.stringify(this.searchHistory));
			} catch (e) {
				console.error('Failed to save search history:', e);
			}
		},
		
		// 清除搜索
		clearSearch() {
			this.searchKeyword = '';
			this.searchResults = [];
		},
		
		// 使用历史记录项
		useHistoryItem(keyword) {
			this.searchKeyword = keyword;
			this.handleSearch();
		},
		
		// 删除单个历史记录
		deleteHistoryItem(index) {
			this.searchHistory.splice(index, 1);
			try {
				uni.setStorageSync('searchHistory', JSON.stringify(this.searchHistory));
			} catch (e) {
				console.error('Failed to save search history:', e);
			}
		},
		
		// 清除所有历史记录
		clearHistory() {
			uni.showModal({
				title: '提示',
				content: '确定要清除所有搜索历史吗？',
				success: (res) => {
					if (res.confirm) {
						this.searchHistory = [];
						try {
							uni.removeStorageSync('searchHistory');
						} catch (e) {
							console.error('Failed to clear search history:', e);
						}
					}
				}
			});
		},
		
		// 跳转到详情页
		goToDetail(item) {
			uni.navigateTo({
				url: "/subpages/location-detail/location-detail?item=" + encodeURIComponent(JSON.stringify(item))
			})
		},
		
		// 返回上一页
		goBack() {
			uni.navigateBack();
		}
	}
}
</script>

<style lang="scss">
.search-container {
	height: 100vh;
  overflow: hidden;
	background-color: #f5f5f5;
	position: relative;
}

.status-bar {
	background-color: #fff;
	width: 100%;
}

.search-header {
	display: flex;
	align-items: center;
	padding: 20rpx 30rpx;
	background: #fff;
	position: sticky;
	top: 0;
	z-index: 100;
	
	.search-box {
		flex: 1;
		height: 80rpx;
		background: #f0f0f0;
		border-radius: 40rpx;
		display: flex;
		align-items: center;
		padding: 0 20rpx;
		
		.search-icon {
			width: 40rpx;
			height: 40rpx;
			margin-right: 20rpx;
		}
		
		.search-input {
			flex: 1;
			height: 80rpx;
			font-size: 28rpx;
		}
		
		.clear-icon {
			width: 40rpx;
			height: 40rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			color: #999;
			font-size: 40rpx;
		}
	}
	
	.cancel-btn {
		padding: 0 30rpx;
		font-size: 32rpx;
		color: #333;
	}
}

.search-history, .hot-search {
	margin: 30rpx;
	background: #fff;
	border-radius: 20rpx;
	padding: 20rpx 30rpx;
	
	.history-header, .hot-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 20rpx;
		
		.history-title, .hot-title {
			font-size: 30rpx;
			font-weight: 500;
			color: #333;
		}
		
		.clear-history {
			font-size: 28rpx;
			color: #999;
		}
	}
	
	.history-list, .hot-list {
		display: flex;
		flex-wrap: wrap;
		
		.history-item, .hot-item {
			background: #f0f0f0;
			padding: 10rpx 20rpx;
			border-radius: 30rpx;
			margin: 10rpx;
			display: flex;
			align-items: center;
			
			.history-text, .hot-text {
				font-size: 26rpx;
				color: #333;
			}
			
			.delete-icon {
				margin-left: 10rpx;
				color: #999;
				font-size: 28rpx;
			}
		}
	}
}

.search-results {
	padding: 20rpx 30rpx;
	height: calc(100vh - 180rpx); /* 调整高度，避免出现滚动条 */
	box-sizing: border-box;
	
	.result-item {
		background: #fff;
		border-radius: 20rpx;
		margin-bottom: 20rpx;
		overflow: hidden;
		display: flex;
		
		.result-image {
			width: 200rpx;
			height: 200rpx;
		}
		
		.result-info {
			flex: 1;
			padding: 20rpx;
			
			.result-name {
				font-size: 32rpx;
				font-weight: 500;
				color: #333;
				margin-bottom: 10rpx;
			}
			
			.result-address {
				font-size: 26rpx;
				color: #666;
				margin-bottom: 10rpx;
			}
			
			.result-tags {
				display: flex;
				flex-wrap: wrap;
				
				.tag {
					font-size: 24rpx;
					color: #4CAF50;
					background: rgba(76, 175, 80, 0.1);
					padding: 4rpx 12rpx;
					border-radius: 20rpx;
					margin-right: 10rpx;
					margin-bottom: 10rpx;
				}
			}
		}
	}
	
	.no-results {
		display: flex;
		flex-direction: column;
		align-items: center;
		padding: 100rpx 0;
		
		.no-results-image {
			width: 200rpx;
			height: 200rpx;
			margin-bottom: 30rpx;
		}
		
		.no-results-text {
			font-size: 30rpx;
			color: #999;
		}
	}
}



.load-more {
	text-align: center;
	padding: 20rpx 0;
	font-size: 28rpx;
	
	text {
		color: #4CAF50;
		
		&:last-child {
			color: #999;
		}
	}
}

.loading {
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 100rpx 0;
	
	.spinner {
		width: 60rpx;
		height: 60rpx;
		border: 6rpx solid #f3f3f3;
		border-top: 6rpx solid #4CAF50;
		border-radius: 50%;
		animation: spin 1s linear infinite;
		margin-bottom: 20rpx;
	}
	
	.loading-text {
		font-size: 30rpx;
		color: #999;
	}
}

@keyframes spin {
	0% { transform: rotate(0deg); }
	100% { transform: rotate(360deg); }
}
</style> 