<template>
	<view class="container">
		<!-- 顶部导航栏 -->
		<view class="nav-bar">
			<view class="status-bar"></view>
			<view class="header">
				<view class="back-btn" @tap="goBack">
					<view class="back-icon">
						<view class="back-arrow"></view>
					</view>
				</view>
				<text class="title">约球详情</text>
			</view>
		</view>

		<!-- 页面内容区域 -->
		<view class="page-content">
			<!-- 加载中 -->
			<view class="loading" v-if="loading">
				<view class="loading-spinner"></view>
				<text>加载中...</text>
			</view>

			<!-- 详情内容 -->
			<block v-else>
				<!-- 活动基本信息 -->
				<view class="event-header">
					<view class="event-title">{{ event.title }}</view>
					<view class="event-status" :class="{
						'status-active': event.status === 'active',
						'status-ended': event.status === 'ended',
						'status-canceled': event.status === 'canceled'
					}">
						{{ getStatusText(event.status) }}
					</view>
				</view>

				<!-- 创建者信息 -->
				<view class="creator-info">
					<image :src="event.creator_avatar || '/static/default-avatar.png'" class="creator-avatar" />
					<view class="creator-detail">
						<text class="creator-name">{{ event.creator_name }}</text>
						<text class="create-time">创建于 {{ formatDateTime(event.created_at) }}</text>
					</view>
				</view>

				<!-- 活动详情 -->
				<view class="info-card">
					<view class="info-item">
						<text class="info-icon iconfont icon-sport">🏀</text>
						<text class="info-label">运动类型：</text>
						<text class="info-value">{{ getSportName(event.sport_type) }}</text>
					</view>
					<view class="info-item">
						<text class="info-icon iconfont icon-time">🕒</text>
						<text class="info-label">活动时间：</text>
						<text class="info-value">{{ formatDateTime(event.event_time) }}</text>
					</view>
					<view class="info-item">
						<text class="info-icon iconfont icon-duration">⏱️</text>
						<text class="info-label">预计时长：</text>
						<text class="info-value">{{ event.duration }}小时</text>
					</view>
					<view class="info-item">
						<text class="info-icon iconfont icon-location">📍</text>
						<text class="info-label">活动地点：</text>
						<text class="info-value">{{ event.location_name }}</text>
					</view>
					<view class="info-item">
						<text class="info-icon iconfont icon-address">🏙️</text>
						<text class="info-label">详细地址：</text>
						<text class="info-value">{{ event.address }}</text>
					</view>
					<view class="info-item">
						<text class="info-icon iconfont icon-people">👥</text>
						<text class="info-label">参与人数：</text>
						<text class="info-value">{{ event.current_participants }}/{{ event.max_participants }}人</text>
					</view>
					<view class="info-item" >
						<text class="info-icon iconfont icon-fee">💰</text>
						<text class="info-label">费用：</text>
						<!-- <text class="info-value">¥{{ event.fee }}/人</text> -->
						<text class="info-value">{{ event.fee=='未知'?'未知':event.fee=='免费'?'免费':'¥'+event.fee+'/人' }}</text>
					</view>
				
					<view class="info-item" v-if="event.description">
						<text class="info-icon iconfont icon-description">📝</text>
						<text class="info-label">活动说明：</text>
						<text class="info-value description">{{ event.description }}</text>
					</view>
				</view>

				<!-- 场地图片 -->
				<view class="images-card" v-if="event.images && event.images.length > 0">
					<view class="card-title">场地图片</view>
					<scroll-view class="images-scroll" scroll-x>
						<view class="images-container">
							<image v-for="(image, index) in JSON.parse(event.images)" :key="index" :src="image" class="location-image"
								mode="aspectFill" @tap="previewImage(index, JSON.parse(event.images))" />
						</view>
					</scroll-view>
				</view>

				<!-- 地图位置 -->
				<view class="map-card">
					<view class="card-title">活动位置</view>
					<view class="map-container">
						<map id="eventMap" class="map" :latitude="event.latitude" :longitude="event.longitude" :markers="markers"
							:scale="16" show-location></map>
					</view>
					<view class="map-actions">
						<view class="distance">距离您: {{ calculateAndGetDistance() }}</view>
						<button class="navigate-btn" @tap="navigate">
							<text class="iconfont icon-navigation">🧭</text>
							导航
						</button>
					</view>
				</view>

				<!-- 参与者列表 -->
				<view class="participants-card">
					<view class="card-title">参与者 ({{ Object.keys(event).includes('participants') ? event.participants.length : '1'
					}}/{{ event.max_participants }})</view>
					<view class="participants-list">
						<view class="participant-item" v-for="(participant, index) in event.participants" :key="index">
							<image :src="participant.avatar || '/static/default-avatar.png'" class="participant-avatar" />
							<text class="participant-name">{{ participant.nickname }}</text>
							<text class="participant-tag creator" v-if="participant.user_id === event.creator_id">发起人</text>
							<!-- 只有已参与的用户可以查看联系方式 -->
							<view class="contact-info" v-if="isJoined">
								<!-- 根据参与者的show_contact状态决定是否显示联系方式 -->
								<template v-if="participant.show_contact== '1' && (participant.phone || participant.wechat)">
									<view class="contact-item" v-if="participant.phone" @tap="callPhone(participant.phone)">
										<text class="contact-icon">📞</text>
										<text class="contact-value">{{ participant.phone }}</text>
									</view>
									<view class="contact-item" v-if="participant.wechat" @tap="copyWechat(participant.wechat)">
										<text class="contact-icon">💬</text>
										<text class="contact-value">{{ participant.wechat }}</text>
									</view>
								</template>
								<view class="contact-item" v-else>
									<text class="contact-icon">🔒</text>
									<text class="contact-value">联系保密</text>
								</view>
							</view>
						</view>
					</view>
				</view>

				<!-- 群聊入口 -->
				<view class="chat-card" v-if="isJoined || event.creator_id.toString() === userInfo.id.toString()">
					<view class="card-title">活动群聊</view>
					<view class="chat-info">
						<view class="chat-item">
							<text class="chat-icon">💬</text>
							<view class="chat-detail">
								<text class="chat-title">{{ event.title }}-活动群</text>
								<text class="chat-subtitle">与其他参与者实时交流</text>
							</view>
							<button class="enter-chat-btn" @tap="enterGroupChat">
								进入群聊
							</button>
						</view>
					</view>
				</view>

				<!-- 用户勾选 -->
				<view class="contact-share-card" v-if="event.creator_id.toString() !== userInfo.id.toString() && event.status === 'active'&&!isJoined">
					<view class="checkbox-item">
						<checkbox :checked="share_contact" @tap="toggleShareContact" />
						<text class="checkbox-label">允许参与者查看我的联系方式</text>
					</view>
				</view>
				
				<!-- 活动操作按钮 -->
				<view class="action-btns">
					<!-- 非创建者操作 -->
					<block v-if="event.creator_id.toString() !== userInfo.id.toString()">
						<button class="join-btn" @tap="joinEvent"
							v-if="event.status === 'active' && !isJoined && event.current_participants < event.max_participants">
							加入活动
						</button>
						<button class="quit-btn" @tap="quitEvent" v-if="event.status === 'active' && isJoined">
							退出活动
						</button>
						<button class="ended-btn" disabled v-else-if="event.status === 'ended'">
							活动已结束
						</button>
						<button class="full-btn" disabled v-else-if="event.current_participants >= event.max_participants">
							人数已满
						</button>
						<button class="canceled-btn" disabled v-else-if="event.status === 'canceled'">
							活动已取消
						</button>
						<button class="share-btn" open-type="share">
							分享活动
						</button>
					</block>


					<!-- 创建者操作 -->
					<block v-else>

						<button class="cancel-btn" @tap="cancelEvent" v-if="event.status === 'active'">
							取消活动
						</button>
						<button class="ended-btn" disabled v-else-if="event.status === 'ended'">
							活动已结束
						</button>
						<button class="full-btn" disabled v-else-if="event.current_participants >= event.max_participants">
							人数已满
						</button>
						<button class="canceled-btn" disabled v-else-if="event.status === 'canceled'">
							活动已取消
						</button>
						<button class="share-btn" open-type="share">
							分享活动
						</button>

					</block>

				</view>
			</block>
		</view>

		<!-- 地图选择弹窗 -->
		<view class="map-modal" v-if="showMapModal" @tap="closeMapModal">
			<view class="map-options" @tap.stop>
				<view class="map-option" @tap="navigate('gaode')">
					<text class="iconfont icon-map">🗺️</text>
					高德地图
				</view>
				<view class="map-option" @tap="navigate('baidu')">
					<text class="iconfont icon-map">🗺️</text>
					百度地图
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import {
	getSportsEventDetail,
	joinSportsEvent,
	quitSportsEvent,
	cancelSportsEvent,
	getAllSports,
	getEventGroup,
	getIMConfig
} from "../../Api/index.js";
import imManager from "../../utils/IMManager.js";

export default {
	data() {
		return {
			sports:[],
			loading: true,
			eventId: '',
			event: {
				id: '',
				title: '',
				sportType: '',
				eventTime: '',
				duration: 2,
				locationName: '',
				address: '',
				latitude: 0,
				longitude: 0,
				max_participants: 0,
				current_participants: 0,
				fee: 0,
				description: '',
				status: 'active',
				createTime: '',
				creator_id: '',
				creatorName: '',
				creatorAvatar: '',
				participants: [],
				distance_meters: 0,
				images: []
			},
			userInfo: {
				id: '',
				nickname: '',
				avatar: ''
			},
			currentLocation: {
				latitude: 0,
				longitude: 0
			},
			isJoined: false,
			markers: [],
			showMapModal: false,
			action: '', // 用于处理页面跳转带的操作参数
			share_contact: true // 默认勾选
		}
	},
	onLoad(options) {
		if(options.id){
			this.eventId = options.id

		}

		// 保存当前选中的运动类型到本地缓存，在返回时使用
		if (options.sportType) {
			uni.setStorageSync('currentSportType', options.sportType)
		}

	
		
		// 获取用户当前位置
		this.$nextTick(() => {
			uni.getLocation({
				type: 'gcj02',
				success: (res) => {
					this.currentLocation = {
						latitude: res.latitude,
						longitude: res.longitude
					}
				},
				fail: (err) => {
					console.log('获取位置失败', err)
				}
			})
		})
		
		// 检查用户是否已登录
		const token = uni.getStorageSync('ydToken')
		if (!token) {
			uni.showModal({
				title: '提示',
				content: '请先登录后再进行约球',
				confirmText: '去登录',
				success: (res) => {
					if (res.confirm) {
						uni.switchTab({
							url: '/pages/profile/profile'
						})
					}
				}
			})
			return
		}
		getAllSports().then(res=>{
				if (res.data.code !== 200) return; 
				this.sports=res.data.data.sports
						})
		this.userInfo = this.$store.state.userinfo
		
	},

	// 分享到微信好友
	onShareAppMessage() {

		return {
			title: `【约球】：${this.event.title} - ${this.event.location_name} - 好兄弟，一起来打球！`,
			path: `subpages/sports-event-detail/sports-event-detail?id=${this.eventId}`,
		}
	},

	// 分享到朋友圈
	onShareTimeline() {
		return {
			title: `【约球】：${this.event.title} - ${this.event.location_name} - 好兄弟，一起来打球！`,
			query: `id=${this.eventId}`,
		}
	},
	watch: {
		userInfo:{
			handler(newVal) {
			  // 加载活动详情
				this.getEventDetail()
			},
			deep: true,
		},
		"$store.state.userinfo": {
			handler(newVal) {
				this.userInfo = newVal
				
				// 检查用户是否已登录
				if (!newVal || !newVal.nickname) {
					uni.showModal({
						title: '提示',
						content: '请先登录后再进行约球',
						confirmText: '去登录',
						success: (res) => {
							if (res.confirm) {
								uni.switchTab({
									url: '/pages/profile/profile'
								})
							}
						}
					})
					return
				}
				
			},
			deep: true,
			
		}
	},

	methods: {
		// 进入群聊
		async enterGroupChat() {
			// 显示加载状态
			uni.showLoading({
				title: '进入群聊中...'
			})

			try {
				// 检查IM登录状态
				const loginStatus = imManager.getLoginStatus()
				console.log('当前IM登录状态:', loginStatus);

				if (!loginStatus.isLoggedIn) {
					console.log('IM未登录，尝试重新初始化...')
					// 尝试重新初始化IM
					try {
						console.log('步骤1: 获取IM配置...')
						const imConfigRes = await getIMConfig()
						console.log('步骤1完成 - IM配置响应:', imConfigRes)

						if (imConfigRes.data.code === 200) {
							const imConfig = imConfigRes.data.data
							console.log('步骤2: IM配置数据解析成功:', imConfig)

							// 验证配置数据
							if (!imConfig.SDKAppID || !imConfig.userID || !imConfig.userSig) {
								console.error('IM配置数据不完整:', {
									hasSDKAppID: !!imConfig.SDKAppID,
									hasUserID: !!imConfig.userID,
									hasUserSig: !!imConfig.userSig
								})
								uni.hideLoading()
								uni.showToast({
									title: 'IM配置数据不完整',
									icon: 'none'
								})
								return
							}

							// 检查是否需要重新初始化
							if (!imManager.tim || imManager.currentUserID !== imConfig.userID) {
								console.log('步骤3: 需要重新初始化IM SDK')
								// 重新初始化IM SDK
								const initSuccess = imManager.init({
									SDKAppID: imConfig.SDKAppID
								})
								console.log('步骤3完成 - IM初始化结果:', initSuccess)

								if (!initSuccess) {
									uni.hideLoading()
									uni.showToast({
										title: 'IM初始化失败',
										icon: 'none'
									})
									return
								}
							} else {
								console.log('步骤3: 跳过初始化，使用现有IM实例')
							}

							// 重新登录IM - 先尝试简化登录
							console.log('步骤4: 开始IM登录...')
							console.log('尝试简化登录方法...')
							const simpleLoginResult = await imManager.simpleLogin(imConfig.userID, imConfig.userSig)
							console.log('简化登录结果:', simpleLoginResult)

							let loginResult
							if (simpleLoginResult.success) {
								loginResult = simpleLoginResult
							} else {
								console.log('简化登录失败，尝试标准登录方法...')
								loginResult = await imManager.login(imConfig.userID, imConfig.userSig)
							}
							console.log('步骤4完成 - 最终IM登录结果:', loginResult)

							if (!loginResult.success) {
								uni.hideLoading()
								uni.showToast({
									title: loginResult.message || 'IM登录失败',
									icon: 'none',
									duration: 3000
								})
								return
							}

							console.log('IM重新初始化和登录成功！')
						} else {
							console.error('获取IM配置失败，响应码:', imConfigRes.data.code)
							uni.hideLoading()
							uni.showToast({
								title: '获取IM配置失败',
								icon: 'none'
							})
							return
						}
					} catch (error) {
						console.error('重新初始化IM失败:', error)
						uni.hideLoading()
						uni.showModal({
							title: '提示',
							content: 'IM初始化失败，请重新登录应用',
							showCancel: false,
							confirmText: '重新登录',
							success: () => {
								uni.reLaunch({
									url: '/pages/profile/profile'
								})
							}
						})
						return
					}
				}

				// 获取活动群组信息
				const groupRes = await getEventGroup(this.eventId)
				if (groupRes.data.code === 200 && groupRes.data.data.groupId) {
					const groupId = groupRes.data.data.groupId

					// 加入群组
					const joinResult = await imManager.joinGroup(groupId)
					uni.hideLoading()

					if (joinResult.success) {
						// 跳转到群聊页面
						uni.navigateTo({
							url: `/subpages/group-chat/group-chat?groupId=${groupId}&groupName=${encodeURIComponent(this.event.title + '-活动群')}`
						})
					} else {
						console.error('加入群组失败:', joinResult.error)
						uni.showToast({
							title: joinResult.message || '加入群聊失败',
							icon: 'none',
							duration: 3000
						})
					}
				} else {
					uni.hideLoading()
					uni.showToast({
						title: '群聊暂未创建，请联系活动发起人',
						icon: 'none',
						duration: 3000
					})
				}
			} catch (error) {
				uni.hideLoading()
				console.error('进入群聊异常:', error)
				uni.showToast({
					title: '进入群聊失败，请检查网络连接',
					icon: 'none',
					duration: 3000
				})
			}
		},

		// 呼叫电话
    callPhone(phone) {
        uni.makePhoneCall({
          phoneNumber: phone.toString()
        });
    },
    // 复制微信号
    copyWechat(wechat) {
        uni.setClipboardData({
            data: wechat,
            success: function() {
                uni.showToast({
                    title: '微信号已复制',
                    icon: 'success'
                });
            }
        });
    },
		// 获取活动详情
		getEventDetail() {
			// 获取用户信息
			// 检查用户是否已登录
			
	

			this.loading = true
			getSportsEventDetail({
				id: this.eventId
			}).then(res => {
				if (res.data.code === 200) {
					this.event = res.data.data.event

					// 设置地图标记
					this.markers = [{
						id: 1,
						latitude: this.event.latitude,
						longitude: this.event.longitude,
						title: this.event.locationName,
						iconPath: '/static/icons/location.svg',
						width: 32,
						height: 32
					}]

					console.log('participants' in this.event);

					if ('participants' in this.event) {
						// 保存参与者信息到Vuex
						this.$store.commit('SAVE_PARTICIPANTS', this.event.participants)

						// 检查用户是否已参与
						this.isJoined = this.event.participants.some(p => {

							return p.user_id.toString() == this.userInfo.id.toString()
						})
					}

					// 不再自动处理跳转时带的操作
				} else {
					uni.showToast({
						title: res.data.message || '获取活动详情失败',
						icon: 'none'
					})
				}
				this.loading = false
			}).catch((err) => {
				console.log(err);

				this.loading = false
				uni.showToast({
					title: '网络错误，请稍后重试',
					icon: 'none'
				})
			})
		},
		// 处理页面跳转时带的操作
		handleAction() {
			// 已移除自动处理操作的逻辑
		},
		// 返回上一页
		goBack() {
			uni.switchTab({ url: '/pages/sports-event/sports-event' })
		},
		// 格式化日期时间
		formatDateTime(timestamp) {
			if (!timestamp) return '未知时间'
			const date = new Date(timestamp)
			const year = date.getFullYear()
			const month = (date.getMonth() + 1).toString().padStart(2, '0')
			const day = date.getDate().toString().padStart(2, '0')
			const hour = date.getHours().toString().padStart(2, '0')
			const minute = date.getMinutes().toString().padStart(2, '0')
			return `${year}-${month}-${day} ${hour}:${minute}`
		},
		// 获取运动名称
		getSportName(sportId) {
			const sports = this.sports || []
			const sport = sports.find(item => item.id === sportId)
			return sport ? sport.sportsName : '未知运动'
		},
		// 获取状态文本
		getStatusText(status) {
			switch (status) {
				case 'active': return '进行中'
				case 'ended': return '已结束'
				case 'canceled': return '已取消'
				default: return '未知'
			}
		},
		// 计算并返回距离文本
		calculateAndGetDistance() {
			// 如果已经有计算好的距离
			if (this.event.distance_meters) {
				return this.formatDistance(this.event.distance_meters);
			}

			// 如果已获取当前位置且活动位置有效
			if (this.currentLocation.latitude && this.event.latitude) {
				const distance = this.calculateDistance(
					this.event.latitude,
					this.event.longitude,
					this.currentLocation.latitude,
					this.currentLocation.longitude
				);

				// 更新活动对象的距离属性
				this.$set(this.event, 'distance_meters', distance);
				return this.formatDistance(distance);
			}

			return '未知距离';
		},

		// 格式化距离显示
		formatDistance(m) {
			if (!m) return '未知距离';

			if (m < 1000) {
				return Math.round(m) + '米';
			} else {
				return (m / 1000).toFixed(1) + '公里';
			}
		},

		// 计算两点间的距离（单位：米）
		calculateDistance(lat1, lng1, lat2, lng2) {
			const EARTH_RADIUS = 6378137; // 地球半径，单位：米

			// 将角度转换为弧度
			const radLat1 = this.degToRad(lat1);
			const radLng1 = this.degToRad(lng1);
			const radLat2 = this.degToRad(lat2);
			const radLng2 = this.degToRad(lng2);

			// 计算差值
			const latDiff = radLat1 - radLat2;
			const lngDiff = radLng1 - radLng2;

			// 使用球面余弦定理计算距离
			let distance = 2 * Math.asin(Math.sqrt(
				Math.pow(Math.sin(latDiff / 2), 2) +
				Math.cos(radLat1) * Math.cos(radLat2) * Math.pow(Math.sin(lngDiff / 2), 2)
			));

			// 转换为米
			distance = distance * EARTH_RADIUS;

			return Math.round(distance);
		},

		// 角度转弧度
		degToRad(degree) {
			return degree * Math.PI / 180;
		},
		// 显示地图选择弹窗
		showMapOptions() {
			this.showMapModal = true
		},
		// 关闭地图选择弹窗
		closeMapModal() {
			this.showMapModal = false
		},
		// 导航
		navigate(type) {
			const latitude = this.event.latitude
			const longitude = this.event.longitude
			const name = this.event.location_name


			uni.openLocation({
				latitude: Number(latitude),
				longitude: Number(longitude),
				name: name,
				address: this.event.address,
				success: () => {
					// this.closeMapModal()
				}
			})

		},
		// 切换分享联系方式状态
		toggleShareContact() {
			this.share_contact = !this.share_contact
		},
		// 加入活动
		joinEvent() {
			// 检查活动开始前30分钟是否允许加入
			const now = new Date().getTime();
			const eventTime = new Date(this.event.event_time).getTime();
			const thirtyMinutes = 30 * 60 * 1000; // 30分钟的毫秒数
			
			if (eventTime - now < thirtyMinutes) {
				uni.showModal({
					title: '提示',
					content: '活动即将开始，已不允许加入',
					showCancel: false
				});
				return;
			}
			
			// 检查用户是否勾选了分享联系方式但没有填写手机号或微信号
			if (this.share_contact && (!this.userInfo.phone && !this.userInfo.wechat)) {
				uni.showModal({
					title: '提示',
					content: '您未设置手机号或微信号，请前往我的页面设置',
					confirmText: '去设置',
					success: (res) => {
						if (res.confirm) {
							uni.reLaunch({
								url: '/pages/profile/profile?showPopup=true'
							})
						}
					}
				})
				return
			}else if (!this.share_contact && (!this.userInfo.nickname && !this.userInfo.avatar)) {
				uni.showModal({
					title: '提示',
					content: '您未设置头像或昵称，请前往我的页面设置',
					confirmText: '去设置',
					success: (res) => {
						if (res.confirm) {
							uni.reLaunch({
								url: '/pages/profile/profile?showPopup=true'
							})
						}
					}
				})
				return
			}
			
			uni.requestSubscribeMessage({
				tmplIds: ['o-8tpZjbcOPkrrw54S7SXX9Erk3qlQ8997NI6Yj2BAY'],
				success: (res) => {
					uni.showLoading({
						title: '加入中...'
					})
					let sub_num = -1
					let str = res['o-8tpZjbcOPkrrw54S7SXX9Erk3qlQ8997NI6Yj2BAY']
					if (str == 'accept') {
						sub_num = 1
					} else {
						sub_num = 0
					}
					
					// 根据用户选择决定是否传递联系方式
					const params = {
						eventId: this.eventId,
						sub_num: sub_num,
						show_contact: this.share_contact ? 1 : 0  // 添加show_contact参数
					}

					// 如果用户勾选了分享联系方式，则传递phone和wechat
					if (this.share_contact) {
						params.phone = this.userInfo.phone || ''
						params.wechat = this.userInfo.wechat || ''
					} else {
						params.phone = ''
						params.wechat = ''
					}
					
					joinSportsEvent(params).then(res => {
						uni.hideLoading()

						if (res.data.code === 200) {
							uni.showToast({
								title: '加入成功',
								icon: 'success'
							})
							// 刷新活动详情
							this.getEventDetail()
						} else {
							uni.showToast({
								title: res.data.message || '加入失败',
								icon: 'none'
							})
						}
					}).catch(() => {
						uni.hideLoading()
						uni.showToast({
							title: '网络错误，请稍后重试',
							icon: 'none'
						})
					})
				}

			})
		},
		// 退出活动
		quitEvent() {
			// 检查活动开始前30分钟是否允许退出
			const now = new Date().getTime();
			const eventTime = new Date(this.event.event_time).getTime();
			const thirtyMinutes = 30 * 60 * 1000; // 30分钟的毫秒数
			
			if (eventTime - now < thirtyMinutes) {
				uni.showModal({
					title: '提示',
					content: '活动即将开始，已不允许退出',
					showCancel: false
				});
				return;
			}
			
			uni.showModal({
				title: '提示',
				content: '确定要退出该活动吗？',
				success: (res) => {
					if (res.confirm) {
						uni.showLoading({
							title: '处理中...'
						})

						quitSportsEvent({
							eventId: this.eventId
						}).then(res => {
							uni.hideLoading()

							if (res.data.code === 200) {
								uni.showToast({
									title: '已退出活动',
									icon: 'success'
								})
								// 刷新活动详情
								this.getEventDetail()
							} else {
								uni.showToast({
									title: res.data.message || '退出失败',
									icon: 'none'
								})
							}
						}).catch(() => {
							uni.hideLoading()
							uni.showToast({
								title: '网络错误，请稍后重试',
								icon: 'none'
							})
						})
					}
				}
			})
		},
		// 取消活动
		cancelEvent() {
			uni.showModal({
				title: '提示',
				content: '确定要取消该活动吗？取消后无法恢复。',
				success: (res) => {
					if (res.confirm) {
						uni.showLoading({
							title: '处理中...'
						})

						cancelSportsEvent({
							eventId: this.eventId
						}).then(res => {
							uni.hideLoading()

							if (res.data.code === 200) {
								uni.showToast({
									title: '活动已取消',
									icon: 'success'
								})
								// 刷新活动详情
								this.getEventDetail()
							} else {
								uni.showToast({
									title: res.data.message || '取消失败',
									icon: 'none'
								})
							}
						}).catch(() => {
							uni.hideLoading()
							uni.showToast({
								title: '网络错误，请稍后重试',
								icon: 'none'
							})
						})
					}
				}
			})
		},



		// 预览图片
		previewImage(index, data) {
			uni.previewImage({
				current: index,
				urls: data
			});
		}
	}
}
</script>

<style lang="scss" scoped>
.container {
	display: flex;
	flex-direction: column;
	height: 100vh;
	background-color: #f5f5f5;
}

.nav-bar {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	z-index: 999;
	background: #4CAF50;
}

.header {
	padding: 20rpx 30rpx;
	display: flex;
	align-items: center;
	position: relative;

	.back-btn {
		position: absolute;
		left: 30rpx;
		color: #fff;
		display: flex;
		align-items: center;
		justify-content: center;
		width: 48rpx;
		height: 48rpx;

		.back-icon {
			width: 36rpx;
			height: 36rpx;
			position: relative;

			.back-arrow {
				width: 20rpx;
				height: 20rpx;
				border-left: 4rpx solid #fff;
				border-bottom: 4rpx solid #fff;
				transform: rotate(45deg);
				position: absolute;
				left: 8rpx;
				top: 8rpx;
			}
		}
	}

	.title {
		flex: 1;
		text-align: center;
		color: #fff;
		font-size: 36rpx;
		font-weight: 500;
	}
}

.page-content {
	flex: 1;
	margin-top: calc(var(--status-bar-height) + 88rpx);
	padding: 30rpx;
	padding-bottom: 60rpx;
	overflow-y: auto;
}

.loading {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	height: 400rpx;
}

.loading-spinner {
	width: 64rpx;
	height: 64rpx;
	border: 6rpx solid #f3f3f3;
	border-top: 6rpx solid #4CAF50;
	border-radius: 50%;
	margin-bottom: 20rpx;
	animation: spin 1s linear infinite;
}

@keyframes spin {
	0% {
		transform: rotate(0deg);
	}

	100% {
		transform: rotate(360deg);
	}
}

.event-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	margin-bottom: 30rpx;
}

.event-title {
	font-size: 40rpx;
	font-weight: bold;
	color: #333333;
	flex: 1;
}

.event-status {
	font-size: 24rpx;
	padding: 8rpx 16rpx;
	border-radius: 8rpx;
}

.status-active {
	background-color: #e8f5e9;
	color: #4CAF50;
}

.status-ended {
	background-color: #f5f5f5;
	color: #999999;
}

.status-canceled {
	background-color: #ffebee;
	color: #f44336;
}

.creator-info {
	display: flex;
	align-items: center;
	margin-bottom: 40rpx;
}

.creator-avatar {
	width: 80rpx;
	height: 80rpx;
	border-radius: 40rpx;
	margin-right: 20rpx;
}

.creator-detail {
	flex: 1;
}

.creator-name {
	font-size: 32rpx;
	font-weight: bold;
	color: #333333;
	display: block;
}

.create-time {
	font-size: 24rpx;
	color: #999999;
}

.info-card,
.map-card,
.participants-card,
.images-card {
	background-color: #ffffff;
	border-radius: 20rpx;
	padding: 30rpx;
	margin-bottom: 30rpx;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.info-item {
	display: flex;
	margin-bottom: 20rpx;
}

.info-item:last-child {
	margin-bottom: 0;
}

.info-icon {
	margin-right: 10rpx;
	color: #666666;
	font-size: 32rpx;
	width: 40rpx;
	text-align: center;
}

.info-label {
	font-size: 28rpx;
	color: #666666;
	width: 160rpx;
}

.info-value {
	font-size: 28rpx;
	color: #333333;
	flex: 1;
}

.info-value.description {
	white-space: pre-wrap;
	line-height: 1.4;
}

.card-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333333;
	margin-bottom: 30rpx;
}

.map-container {
	height: 400rpx;
	border-radius: 16rpx;
	overflow: hidden;
	margin-bottom: 20rpx;
}

.map {
	width: 100%;
	height: 100%;
}

.map-actions {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.distance {
	font-size: 28rpx;
	color: #666666;
}

.navigate-btn {
	background-color: #4CAF50;
	color: #ffffff;
	padding: 12rpx 24rpx;
	border-radius: 40rpx;
	font-size: 28rpx;
	margin: 0;
	line-height: 1.2;
	display: flex;
	align-items: center;
}

.navigate-btn .iconfont {
	margin-right: 10rpx;
}

.participants-list {
	display: flex;
	flex-wrap: wrap;
}

.participant-item {
	display: flex;
	flex-direction: column;
	align-items: center;
	width: 25%;
	margin-bottom: 30rpx;
	position: relative;
}

.participant-avatar {
	width: 100rpx;
	height: 100rpx;
	border-radius: 50rpx;
	margin-bottom: 10rpx;
}

.participant-name {
	font-size: 24rpx;
	color: #333333;
	text-align: center;
	width: 100%;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
}

.participant-tag {
	position: absolute;
	top: 0;
	right: 20rpx;
	font-size: 20rpx;
	padding: 4rpx 8rpx;
	border-radius: 20rpx;
	background-color: #4CAF50;
	color: #ffffff;
}

.contact-info {
	width: 100%;
	margin-top: 10rpx;
}

.contact-item {
	font-size: 20rpx;
	color: #666;
	text-align: center;
	margin-top: 10rpx;
	padding: 8rpx 16rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	background-color: #f5f5f5;
	border-radius: 30rpx;
}

.contact-icon {
	margin-right: 1%;
	font-size: 20rpx;
}

.contact-value {
	color: #333;
	font-weight: 500;
}

.action-btns {
	padding: 30rpx 0;
}

.join-btn,
.quit-btn,
.cancel-btn,
.share-btn,
.full-btn,
.ended-btn,
.canceled-btn {
	width: 100%;
	padding: 24rpx 0;
	border-radius: 50rpx;
	font-size: 32rpx;
	margin-bottom: 30rpx;
}

.join-btn {
	background-color: #4CAF50;
	color: #ffffff;
}

.quit-btn {
	background-color: #FF9800;
	color: #ffffff;
}

.cancel-btn {
	background-color: #F44336;
	color: #ffffff;
}

.share-btn {
	background-color: #2196F3;
	color: #ffffff;
}

.full-btn,
.ended-btn,
.canceled-btn {
	background-color: #f0f0f0;
	color: #999999;
}

.map-modal {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-color: rgba(0, 0, 0, 0.5);
	z-index: 1000;
	display: flex;
	align-items: center;
	justify-content: center;
}

.map-options {
	width: 80%;
	max-width: 600rpx;
	background-color: #ffffff;
	border-radius: 20rpx;
	overflow: hidden;
}

.map-option {
	padding: 30rpx;
	display: flex;
	align-items: center;
	border-bottom: 2rpx solid #f0f0f0;
}

.map-option:last-child {
	border-bottom: none;
}

.map-option .iconfont {
	margin-right: 20rpx;
	font-size: 40rpx;
	color: #4CAF50;
}

.images-scroll {
	width: 100%;
}

.images-container {
	display: flex;
	padding: 10rpx 0;
}

.location-image {
	width: 240rpx;
	height: 180rpx;
	margin-right: 15rpx;
	border-radius: 8rpx;
	flex-shrink: 0;
}

.contact-share-card {
	background-color: #ffffff;
	border-radius: 20rpx;
	padding: 30rpx;
	margin-bottom: 30rpx;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.checkbox-item {
	display: flex;
	align-items: center;
}

.checkbox-label {
	font-size: 28rpx;
	color: #333;
	margin-left: 16rpx;
}

/* 群聊卡片样式 */
.chat-card {
	background-color: #ffffff;
	border-radius: 20rpx;
	padding: 30rpx;
	margin-bottom: 30rpx;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.chat-info {
	margin-top: 20rpx;
}

.chat-item {
	display: flex;
	align-items: center;
	padding: 20rpx;
	background-color: #f8f9fa;
	border-radius: 16rpx;
}

.chat-icon {
	font-size: 40rpx;
	margin-right: 20rpx;
}

.chat-detail {
	flex: 1;
	display: flex;
	flex-direction: column;
}

.chat-title {
	font-size: 28rpx;
	color: #333333;
	font-weight: 500;
	margin-bottom: 8rpx;
}

.chat-subtitle {
	font-size: 24rpx;
	color: #666666;
}

.enter-chat-btn {
	background-color: #4CAF50;
	color: #ffffff;
	font-size: 26rpx;
	padding: 16rpx 24rpx;
	border-radius: 20rpx;
	border: none;
	min-width: 120rpx;
}

.enter-chat-btn:active {
	background-color: #45a049;
}
</style>