<template>
	<view class="container">
		<!-- 页面头部 -->
		<view class="nav-bar">
			<view class="status-bar"></view>
			<view class="header">
				<view class="back-btn" @tap="goBack">
					<image class="back-icon" src="/static/icons/arrow-left.svg" mode="aspectFit"></image>
				</view>
				<text class="title">设置</text>
				<view class="placeholder"></view>
			</view>
		</view>

		<!-- 页面内容区域 -->
		<view class="page-content">
			<!-- 设置菜单 -->
			<view class="settings-list">
				<view class="settings-group">
					<view class="settings-item" @tap="clearCache">
						<text class="settings-text">清除缓存</text>
						<image class="arrow-icon" src="/static/icons/arrow-right.svg" mode="aspectFit"></image>
					</view>
					<view class="settings-item" @tap="aboutUs">
						<text class="settings-text">关于我们</text>
						<image class="arrow-icon" src="/static/icons/arrow-right.svg" mode="aspectFit"></image>
					</view>
					<view class="settings-item" @tap="privacyPolicy">
						<text class="settings-text">隐私政策</text>
						<image class="arrow-icon" src="/static/icons/arrow-right.svg" mode="aspectFit"></image>
					</view>
				</view>
				
				<view class="logout-btn" @tap="logout" v-if="isLoggedIn">退出登录</view>
			</view>
		</view>
	</view>
</template>

<script>
	import imManager from "../../utils/IMManager.js"

	export default {
		data() {
			return {
				isLoggedIn: false
			}
		},
		onShow() {
			// 检查登录状态
			this.checkLoginStatus();
		},
		methods: {
			// 检查登录状态
			checkLoginStatus() {
				const token = uni.getStorageSync('ydToken');
				this.isLoggedIn = !!token;
			},
			
			// 返回上一页
			goBack() {
				uni.navigateBack();
			},
			
			// 清除缓存
			clearCache() {
				uni.showModal({
					title: '提示',
					content: '确定要清除缓存吗？',
					success: (res) => {
						if (res.confirm) {
							// 保留登录信息，清除其他缓存
							const token = uni.getStorageSync('ydToken');
							const favorites = uni.getStorageSync('favorites');
							const selectedSports = uni.getStorageSync('selectedSports');
							
							uni.clearStorageSync();
							
							// 恢复登录信息
							if (token) {
								uni.setStorageSync('ydToken', token);
							}
							if (favorites) {
								uni.setStorageSync('favorites', favorites);
							}
              if (selectedSports) {
								uni.setStorageSync('selectedSports', selectedSports);
							}
							
							uni.showToast({
								title: '缓存已清除',
								icon: 'success'
							});
						}
					}
				});
			},
			
			// 关于我们
			aboutUs() {
				uni.navigateTo({
					url: '/subpages/about/about'
				});
			},

			// 隐私政策
			privacyPolicy() {
				uni.navigateTo({
					url: '/subpages/privacy/privacy'
				});
			},
			
			// 退出登录
			async logout() {
				uni.showModal({
					title: '提示',
					content: '确定要退出登录吗？',
					success: async (res) => {
						if (res.confirm) {
							// 显示退出中状态
							uni.showLoading({
								title: '退出中...'
							});

							try {
								// 清理IM状态
								await imManager.cleanup();

								// 清除登录信息
								uni.removeStorageSync('ydToken');
								// 清除用户信息
								this.$store.state.userinfo = {};

								uni.hideLoading();
								uni.showToast({
									title: '已退出登录',
									icon: 'success'
								});

								// 返回个人中心页面
								setTimeout(() => {
									uni.reLaunch({
										url: '/pages/profile/profile'
									});
								}, 1500);
							} catch (error) {
								console.error('退出登录时清理IM失败:', error);

								// 即使IM清理失败，也要完成退出登录
								uni.removeStorageSync('ydToken');
								this.$store.state.userinfo = {};

								uni.hideLoading();
								uni.showToast({
									title: '已退出登录',
									icon: 'success'
								});

								setTimeout(() => {
									uni.reLaunch({
										url: '/pages/profile/profile'
									});
								}, 1500);
							}
						}
					}
				});
			}
		}
	}
</script>

<style lang="scss">
	.container {
		min-height: 100vh;
		background-color: #f5f5f5;
	}

	.nav-bar {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		z-index: 999;
		background: #4CAF50;
	}
	
	.status-bar {
		height: var(--status-bar-height);
	}
	
	.header {
		display: flex;
		align-items: center;
		height: 90rpx;
		padding: 0 30rpx;
		
		.back-btn {
			width: 60rpx;
			height: 60rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			
			.back-icon {
				width: 40rpx;
				height: 40rpx;
				filter: brightness(10);
			}
		}
		
		.title {
			flex: 1;
			text-align: center;
			font-size: 36rpx;
			color: #fff;
			font-weight: 500;
		}
		
		.placeholder {
			width: 60rpx;
		}
	}

	.page-content {
		padding-top: calc(var(--status-bar-height) + 90rpx);
		padding-bottom: 30rpx;
	}

	.settings-list {
		padding: 30rpx;
	}
	
	.settings-group {
		background: #fff;
		border-radius: 16rpx;
		overflow: hidden;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
		margin-bottom: 30rpx;
	}

	.settings-item {
		position: relative;
		padding: 30rpx;
		display: flex;
		align-items: center;
		justify-content: space-between;
		border-bottom: 1rpx solid #f0f0f0;
		transition: all 0.3s ease;

		&:last-child {
			border-bottom: none;
		}
		
		&:active {
			background: #fafafa;
		}

		.settings-text {
			font-size: 28rpx;
			color: #333;
		}

		.arrow-icon {
			width: 32rpx;
			height: 32rpx;
		}
	}
	
	.logout-btn {
		margin-top: 60rpx;
		background: #fff;
		color: #ff4d4f;
		font-size: 32rpx;
		text-align: center;
		padding: 30rpx 0;
		border-radius: 16rpx;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
		transition: all 0.3s ease;
		
		&:active {
			background: #fafafa;
		}
	}
</style> 