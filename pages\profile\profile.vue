<template>
	<view class="container">
		<!-- 个人信息头部 -->
		<view class="nav-bar">
			<view class="status-bar"></view>
			<view class="profile-header" v-if="isLoggedIn">
				<view class="avatar" @tap="editProfile">
					<image class="user-icon" :src="userInfo.avatar || '/static/icons/user.svg'" mode="aspectFit">
					</image>
				</view>
				<text class="username" @tap="editProfile">{{ userInfo.nickname||'运动用户' }}</text>
				<view class="user-stats" >
					<view class="stat-item" @tap="Navigate('/subpages/my-locations/my-locations')">
						<text class="stat-value">{{ userInfo.posts }}</text>
						<text class="stat-label">发布</text>
					</view>
					<view class="stat-item" @tap="Navigate('/subpages/favorites/favorites')">
						<text class="stat-value">{{ userInfo.favorites }}</text>
						<text class="stat-label">收藏</text>
					</view>

				</view>
			</view>

			<!-- 未登录状态 -->
			<view class="profile-header not-logged-in" v-else>
				<view class="avatar">
					<image class="user-icon" src="/static/icons/user.svg" mode="aspectFit"></image>
				</view>
				<text class="login-tip">您尚未登录</text>
				<button class="login-btn" @tap="wechatLogin">微信一键登录</button>
			</view>
		</view>

		<!-- 页面内容区域 -->
		<view class="page-content">
			<!-- 功能菜单 -->
			<view class="menu-list">
				<view class="menu-item" @tap="Navigate('/subpages/search/search')">
					<image class="menu-icon" src="/static/icons/search.svg" mode="aspectFit"></image>
					<text class="menu-text">搜索场地</text>
					<image class="arrow-icon" src="/static/icons/arrow-right.svg" mode="aspectFit"></image>
				</view>
				<view class="menu-item" @tap="Navigate('/subpages/add-location/add-location')" v-if="isLoggedIn">
					<image class="menu-icon" src="/static/icons/add.svg" mode="aspectFit"></image>
					<text class="menu-text">提交场地信息</text>
					<image class="arrow-icon" src="/static/icons/arrow-right.svg" mode="aspectFit"></image>
				</view>
				<view class="menu-item" @tap="Navigate('/subpages/favorites/favorites')" v-if="false">
					<image class="menu-icon" src="/static/icons/heart.svg" mode="aspectFit"></image>
					<text class="menu-text">我的收藏</text>
					<image class="arrow-icon" src="/static/icons/arrow-right.svg" mode="aspectFit"></image>
				</view>
				<view class="menu-item" v-if="false">
					<image class="menu-icon" src="/static/icons/heart.svg" mode="aspectFit"></image>
					<text class="menu-text">我的约球</text>
					<image class="arrow-icon" src="/static/icons/arrow-right.svg" mode="aspectFit"></image>
				</view>
				<view class="menu-item" @tap="Navigate('/subpages/my-locations/my-locations')" v-if="isLoggedIn">
					<image class="menu-icon" src="/static/icons/location.svg" mode="aspectFit"></image>
					<text class="menu-text">我的场地</text>
					<image class="arrow-icon" src="/static/icons/arrow-right.svg" mode="aspectFit"></image>
				</view>
				<!-- <view class="menu-item" @tap="Navigate('/pages/history/history')">
					<image class="menu-icon" src="/static/icons/history.svg" mode="aspectFit"></image>
					<text class="menu-text">浏览历史</text>
					<image class="arrow-icon" src="/static/icons/arrow-right.svg" mode="aspectFit"></image>
				</view> -->
				<view class="menu-item" @tap="Navigate('/subpages/settings/settings')">
					<image class="menu-icon" src="/static/icons/settings.svg" mode="aspectFit"></image>
					<text class="menu-text">设置</text>
					<image class="arrow-icon" src="/static/icons/arrow-right.svg" mode="aspectFit"></image>
				</view>
				<view class="menu-item" @tap="contactService">
					<image class="menu-icon" src="/static/icons/service.svg" mode="aspectFit"></image>
					<text class="menu-text">联系客服</text>
					<image class="arrow-icon" src="/static/icons/arrow-right.svg" mode="aspectFit"></image>
					<button type="primary" open-type="contact" style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; opacity: 0; z-index: 9;"></button>
				</view>
			</view>
		</view>

		<!-- 获取用户信息弹窗 -->
		<view class="profile-popup" v-if="showProfilePopup">
			<view class="popup-mask"></view>
			<view class="popup-content">
				<view class="popup-close" v-if="isLoggedIn && userInfo.nickname" @tap="closePopup">
					<text class="close-icon">×</text>
				</view>
				<view class="popup-avatar">
					<button @click="chooseava"  class="choose-avatar-button"
						open-type="chooseAvatar" style="border-radius: 50%;padding: 0;" @chooseavatar="onChooseavatar">
						<text v-if="!tempUserInfo.avatar">选择头像</text>
						<image v-else :src="tempUserInfo.avatar"  mode=""></image>
						</button>
					
					<view class="camera-icon" >
						<text class="iconfont icon-camera"></text>
					</view>
				</view>

				<view class="popup-nickname">
					<view class="nickname-label">昵称</view>
					<view class="nickname-input">
						<input type="nickname" placeholder="请输入昵称" v-model="tempUserInfo.nickname" />
						<text class="edit-icon">✎</text>
					</view>
				</view>
				
				<view class="popup-nickname">
					<view class="nickname-label">手机</view>
					<view class="nickname-input">
						<input type="number" placeholder="请输入约球联系电话(选填)" v-model="tempUserInfo.phone" />
						<text class="edit-icon">✎</text>
					</view>
				</view>
				
				<view class="popup-nickname">
					<view class="nickname-label">微信</view>
					<view class="nickname-input">
						<input type="text" placeholder="请输入约球微信号(选填)" v-model="tempUserInfo.wechat" />
						<text class="edit-icon">✎</text>
					</view>
				</view>

				<view class="popup-tips">
					<view class="tips-icon">
						<image src="/static/images/wx.png" mode="aspectFit"></image>
					</view>
					<view class="tips-content">
						<view class="tips-main">99%的用户选择<text
								style="font-style: normal;color: #C51310;">使用微信头像和昵称</text></view>
						<text class="tips-sub">便于报告问题沟通和结算打款</text>
					</view>
				</view>

				<button class="confirm-btn" @tap="confirmUserProfile">{{ isLoggedIn && userInfo.id ? '保存修改' : '确定登录' }}</button>
			</view>
		</view>
		<canvas 
		  style="width: 100%; height: 100%; position: fixed; top: -9999px; left: -9999px;" 
		  canvas-id="compressCanvas" 
		  id="compressCanvas"
		></canvas>
	</view>
</template>

<script>
	import {
		compressImage,
		getBase64
	} from "@/utools/index.js"
	import {
		wxLogin,
		updateUserInfo,
		getIMConfig
	} from "@/Api/index.js"
	import Mixins from "../../minix/index.js"
	import imManager from "../../utils/IMManager.js"
	export default {
		  mixins:[Mixins],
		data() {
			return {
				canvasWidth:"",
				canvasHeight:"",
				isLoggedIn: false,
				showProfilePopup: false,
				userInfo: {
					nickname: '用户名',
					avatar: '',
					favorites: 0,
					posts: 0,
					phone: '',
					wechat: ''
				},
				tempUserInfo: {
					nickname: '',
					avatar: '',
					phone: '',
					wechat: ''
				}
			}
		},
		onShow() {
			// 每次页面显示时更新收藏数量
			this.updateFavoriteCount();
		},
		watch: {
			"$store.state.userinfo": {
				handler(newVal) {
					this.userInfo = newVal

					if ((!newVal.avatar || !newVal.nickname) && uni.getStorageSync('ydToken')) {
						this.isLoggedIn = true
						this.showProfilePopup = true
					} else if (uni.getStorageSync('ydToken') && newVal.id) {
						this.isLoggedIn = true
					} else {
						this.showProfilePopup = false
					}
				},
				deep: true,
				immediate: true
			}
		},
		onLoad(o) {
			if (o.flag) {
				uni.showToast({
					title: '请先登录!',
					icon: 'none'
				})
			}else if(o.showPopup){
				this.tempUserInfo = {
						nickname: this.userInfo.nickname || '',
						avatar: this.userInfo.avatar || '',
						phone: this.userInfo.phone || '',
						wechat: this.userInfo.wechat || ''
					};
					this.showProfilePopup = true;
			}
			
			// 初始化时更新收藏数量
			this.updateFavoriteCount();
		},
		methods: {
			// 更新收藏数量
			updateFavoriteCount() {
				try {
					const favorites = uni.getStorageSync('favorites') || [];
					if (Array.isArray(favorites) && this.userInfo) {
						this.userInfo.favorites = favorites.length;
					}
				} catch (e) {
					console.error('Failed to update favorite count:', e);
				}
			},
			// 关闭弹窗
			closePopup() {
				this.showProfilePopup = false;
			},
			// 编辑个人资料
			editProfile() {
				if (this.isLoggedIn) {
					this.tempUserInfo = {
						nickname: this.userInfo.nickname || '',
						avatar: this.userInfo.avatar || '',
						phone: this.userInfo.phone || '',
						wechat: this.userInfo.wechat || ''
					};
					this.showProfilePopup = true;
				}
			},
			Navigate(item) {
				uni.navigateTo({
					url: item
				})
			},
			chooseava() {
				uni.showToast({
					title: "选择“用微信头像”",
					icon: "none"
				})
			},
			async onChooseavatar(e) {
				// 转 base 64格式
				let url = e.detail.avatarUrl
				try {
					const compressedBlob = await compressImage(url);
					const base64 = await getBase64(compressedBlob);
					this.tempUserInfo.avatar = base64
				} catch (error) {
					console.error("压缩失败:", error);
				}
				// uni.getFileSystemManager().readFile({
				// 	filePath: e.detail.avatarUrl,
				// 	encoding: 'base64',
				// 	success: r => {
				// 		this.tempUserInfo.avatar = "data:image/jpeg;base64," + r.data
				// 	},
				// 	fail: (err) => {
				// 		uni.hideLoading()
				// 		console.log(err);
				// 	}
				// })

			},


			// 微信登录
			async wechatLogin() {
				// 显示加载中
				// 调用微信登录
				await new Promise((resolve, reject) => {
					uni.showLoading({
						title: '登录中...'
					})
					uni.login({
						provider: 'weixin',
						success: async (loginRes) => {
							console.log(1);
							let {
								data
							} = await wxLogin({
								code: loginRes.code
							})
							// 存储token到本地
							uni.setStorageSync('ydToken', data.data.token)
							this.$store.state.userinfo = data.data.user

							// 初始化和登录IM
							await this.initAndLoginIM()

							uni.hideLoading()

							// 弹窗提示
							uni.showToast({
								title: '登录成功',
								icon: 'success'
							})
							resolve()
						},
						fail: (err) => {
							console.error('微信登录失败', err)
							uni.hideLoading()
							uni.showToast({
								title: '微信登录失败',
								icon: 'none'
							})
						}
					})
				})
			},

			// 初始化和登录IM
			async initAndLoginIM() {
				try {
					// 检查是否已经登录IM
					const loginStatus = imManager.getLoginStatus()
					if (loginStatus.isLoggedIn) {
						console.log('IM已经登录，跳过初始化')
						return
					}

					// 获取IM配置
					const imConfigRes = await getIMConfig()
					console.log('IM配置响应:', imConfigRes)
					if (imConfigRes.data.code === 200) {
						const imConfig = imConfigRes.data.data
						console.log('IM配置数据:', imConfig)

						// 初始化IM SDK
						const initSuccess = imManager.init({
							SDKAppID: imConfig.SDKAppID
						})

						if (initSuccess) {
							// 登录IM
							const loginResult = await imManager.login(imConfig.userID, imConfig.userSig)
							if (loginResult.success) {
								console.log('IM登录成功')
							} else {
								console.error('IM登录失败:', loginResult.error)
							}
						} else {
							console.error('IM初始化失败')
						}
					} else {
						console.error('获取IM配置失败:', imConfigRes.data.message)
					}
				} catch (error) {
					console.error('IM初始化和登录异常:', error)
				}
			},

			// 确认用户资料
			async confirmUserProfile() {
				// 构造用户信息
				if(this.tempUserInfo.nickname == ''|| this.tempUserInfo.avatar == ''){
					return uni.showToast({
						title: '请完善资料',
						icon: 'none'
					
					})
				}
				// 更新用户信息
				this.$store.state.userinfo = {
					...this.userInfo,
					...this.tempUserInfo
				}
				// 调用更新接口
				let res = await updateUserInfo(this.$store.state.userinfo)
				this.showProfilePopup = false
				this.$notice('设置成功')
			},

			// 联系客服
			contactService() {
				// 已通过open-type="contact"实现，此方法可以保留但不再需要实际功能
				// uni.showModal({
				// 	title: '联系客服',
				// 	content: '客服电话：************',
				// 	confirmText: '拨打',
				// 	success: (res) => {
				// 		if (res.confirm) {
				// 			uni.makePhoneCall({
				// 				phoneNumber: '4001234567'
				// 			})
				// 		}
				// 	}
				// })
			}
		}
	}
</script>

<style lang="scss">
	.choose-avatar-button {
		background-size: 100% 100%;
		height: 100%;
		font-size: 24rpx;
		display: flex;
		justify-content: center;
		align-items: center;
	}

	.container {
		min-height: 100vh;
		background-color: #f5f5f5;
	}

	.nav-bar {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		z-index: 999;
		background: #4CAF50;
	}

	.profile-header {
		padding: 40rpx;
		color: #fff;
		text-align: center;

		&.not-logged-in {
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
		}

		.avatar {
			width: 160rpx;
			height: 160rpx;
			border-radius: 50%;
			background: #fff;
			margin: 0 auto 30rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			overflow: hidden;

			.user-icon {
				width: 100%;
				height: 100%;
			}
		}

		.username {
			font-size: 36rpx;
			margin-bottom: 10rpx;
		}

		.login-tip {
			font-size: 32rpx;
			margin-bottom: 30rpx;
		}

		.login-btn {
			background: #fff;
			color: #4CAF50;
			font-size: 32rpx;
			padding: 16rpx 60rpx;
			border-radius: 50rpx;
			margin: 0;
			border: none;
			display: flex;
			align-items: center;

			&:after {
				border: none;
			}
		}

		.user-stats {
			display: flex;
			justify-content: center;
			gap: 40rpx;
			margin-top: 30rpx;

			.stat-item {
				text-align: center;

				.stat-value {
					font-size: 36rpx;
					font-weight: 500;
					display: block;
				}

				.stat-label {
					font-size: 24rpx;
					opacity: 0.8;
				}
			}
		}
	}

	.page-content {
		padding-top: calc(var(--status-bar-height) + 400rpx);
	}

	.menu-list {
		padding: 30rpx;

		.menu-item {
			position: relative;
			background: #fff;
			padding: 30rpx;
			margin-bottom: 2rpx;
			display: flex;
			align-items: center;
			border-radius: 16rpx;
			box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
			transition: all 0.3s ease;

			&:active {
				transform: scale(0.98);
				background: #fafafa;
			}

			.menu-icon {
				width: 44rpx;
				height: 44rpx;
				margin-right: 24rpx;
			}

			.arrow-icon {
				width: 32rpx;
				height: 32rpx;
				margin-left: auto;
			}

			.menu-text {
				font-size: 28rpx;
				color: #333;
				flex: 1;
			}
		}
	}

	/* 获取用户信息弹窗 */
	.profile-popup {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		z-index: 9999;

		.popup-mask {
			position: absolute;
			top: 0;
			left: 0;
			right: 0;
			bottom: 0;
			background: rgba(0, 0, 0, 0.5);
		}

		.popup-content {
			position: absolute;
			padding: 0 32rpx 32rpx;
			left: 50%;
			top: 50%;
			transform: translate(-50%, -50%);
			width: 650rpx;
			background: #fff;
			border-radius: 20rpx;
			overflow: hidden;
			box-shadow: 0 0 20rpx rgba(0, 0, 0, 0.1);
		}
		
		.popup-close {
			position: absolute;
			top: 20rpx;
			right: 20rpx;
			width: 60rpx;
			height: 60rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			z-index: 10;
			
			.close-icon {
				font-size: 48rpx;
				color: #999;
				font-weight: bold;
			}
		}

		.popup-avatar {
			width: 160rpx;
			height: 160rpx;
			margin: 50rpx auto 20rpx;
			position: relative;

			image {
				width: 100%;
				height: 100%;
				border-radius: 50%;
				background: #f0f0f0;
			}

			.camera-icon {
				position: absolute;
				right: 0;
				bottom: 0;
				width: 50rpx;
				height: 50rpx;
				background: #07C160;
				border-radius: 50%;
				display: flex;
				align-items: center;
				justify-content: center;
				color: #fff;
				font-size: 26rpx;
			}
		}

		.popup-nickname {
			display: flex;
			align-items: center;
			background: #F6F6F6;

			.nickname-label {
				font-size: 28rpx;
				color: #666;
				display: block;
				flex-shrink: 0;
				margin-bottom: 4rpx;
				height: 100%;
				padding: 0 10px;
				font-size: 32rpx;
			}

			.nickname-input {
				display: flex;
				align-items: center;
				padding: 10rpx 0;
				padding-right: 10rpx;
				justify-content: space-between;
				width: 100%;

				input {
					flex: 1;
					height: 70rpx;
					font-size: 32rpx;
				}

				.edit-icon {
					font-size: 40rpx;
					color: #999;
				}
			}
		}

		.popup-tips {
			padding: 30rpx 40rpx;
			display: flex;
			align-items: center;

			.tips-icon {
				margin-right: 20rpx;

				image {
					width: 80rpx;
					height: 80rpx;
				}
			}

			.tips-content {
				flex: 1;

				.tips-main {
					font-size: 26rpx;
					color: #333;
					display: block;
					font-weight: 500;
				}

				.tips-sub {
					font-size: 24rpx;
					color: #666;
					display: block;
					margin-top: 6rpx;
				}
			}
		}

		.confirm-btn {
			margin: 0;
			height: 100rpx;
			line-height: 100rpx;
			font-size: 34rpx;
			color: #fff;
			background: #07C160;
			border-radius: 0;

			&:after {
				border: none;
			}
		}
	}
</style>