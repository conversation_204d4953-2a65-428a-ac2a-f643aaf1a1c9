<template>
  <view class="container">
    <!-- 顶部导航栏 -->
    <view class="nav-bar">
      <view class="status-bar"></view>
      <view class="header">
        <view class="back-btn" @tap="goBack">
          <view class="back-icon">
            <view class="back-arrow"></view>
          </view>
        </view>
        <text class="title">我的收藏</text>
      </view>
    </view>

    <!-- 页面内容区域 -->
    <view class="page-content">
      <!-- 无收藏时展示 -->
      <view class="empty-state" v-if="favoriteLocations.length === 0">
        <image src="/static/icons/empty-heart.svg" mode="aspectFit" class="empty-icon"></image>
        <text class="empty-text">暂无收藏场地</text>
        <button class="explore-btn" @tap="goToExplore">去发现场地</button>
      </view>

      <!-- 收藏列表 -->
      <view class="location-list" v-else>
        <view class="location-card" v-for="(item, index) in favoriteLocations" :key="index" @tap="goToDetail(item)">
          <!-- 场地图片 -->
          <!-- <image :src="getLocationImage(item)" mode="" class="location-image"></image> -->
          <swiper class="location-image" indicator-dots autoplay circular
            v-if="item.images && item.images.length > 0">
            <swiper-item v-for="(img, imgIndex) in item.images" :key="imgIndex">
              <image :src="img" mode="" class="location-image-item" style="width: 100%;height: 100%;" lazy-load="true" />
            </swiper-item>
          </swiper>
          <!-- 场地信息 -->
          <view class="location-info">
            <view class="location-header">
              <text class="location-name">{{ item.name }}</text>
              <view class="type-tag">{{ item.typeName }}</view>
            </view>
            
            <view class="location-address">
              <image src="/static/icons/map-pin.svg" class="icon-location"></image>
              <text class="address-text">{{ item.address }}</text>
            </view>
            
            <view class="location-footer">
              <text v-if="item.feeType == 'free'" class="price free">免费</text>
              <text v-else-if="item.feeType == 'unknown'" class="price free">未知</text>
              <text v-else-if="item.feeType == 'hourly'" class="price">¥{{ item.hourlyPrice }}/小时</text>
              <text v-else-if="item.feeType == 'perTime'" class="price">¥{{ item.perTimePrice }}/次</text>
              
              <view class="distance" v-if="item.distance_meters">
                <text>{{ formatDistance(item.distance_meters) }}</text>
              </view>
            </view>
          </view>
          
          <!-- 取消收藏按钮 -->
          <view class="unfavorite-btn" @tap.stop="toggleFavorite(item)">
            <image src="/static/icons/heart-filled.svg" class="icon-heart"></image>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
	import Mixins from "../../minix/index.js"
export default {
	  mixins:[Mixins],
  data() {
    return {
      favoriteLocations: []
    }
  },
  onShow() {
    // 每次页面显示时重新获取收藏列表
    if(uni.getStorageSync('ydToken')){
      this.loadFavorites();
    }
  },
  methods: {
    // 加载收藏列表
    loadFavorites() {
      try {
        const favorites = uni.getStorageSync('favorites') || [];
        if (Array.isArray(favorites)) {
          this.favoriteLocations = favorites;
        } else {
          this.favoriteLocations = [];
        }
      } catch (e) {
        console.error('Failed to load favorites:', e);
        this.favoriteLocations = [];
      }
    },
    
    // 获取场地图片
    getLocationImage(item) {
      try {
        if (typeof item.images === 'string') {
          const images = JSON.parse(item.images);
          return images && images.length > 0 ? images[0] : '/static/images/default-location.jpg';
        } else if (Array.isArray(item.images) && item.images.length > 0) {
          return item.images[0];
        }
        return '/static/images/default-location.jpg';
      } catch (e) {
        return '/static/images/default-location.jpg';
      }
    },
    
    // 跳转到场地详情
    goToDetail(item) {
      uni.navigateTo({
        url: '/pages/location-detail/location-detail?item=' + encodeURIComponent(JSON.stringify(item))
      });
    },
    
    // 切换收藏状态（取消收藏）
    toggleFavorite(item) {
      uni.showModal({
        title: '取消收藏',
        content: '确认要取消收藏此场地吗？',
        success: (res) => {
          if (res.confirm) {
            try {
              let favorites = uni.getStorageSync('favorites') || [];
              favorites = favorites.filter(favorite => favorite.id !== item.id);
              uni.setStorageSync('favorites', favorites);
              
              // 更新用户收藏数量
              let userInfo = this.$store.state.userinfo || {};
              userInfo.favorites = favorites.length;
              this.$store.state.userinfo = userInfo;
              
              // 更新列表
              this.favoriteLocations = favorites;
              
              uni.showToast({
                title: '已取消收藏',
                icon: 'none'
              });
            } catch (e) {
              console.error('Failed to remove favorite:', e);
              uni.showToast({
                title: '操作失败，请重试',
                icon: 'none'
              });
            }
          }
        }
      });
    },
    
    // 返回上一页
    goBack() {
      uni.navigateBack();
    },
    
    // 跳转到探索页面（首页）
    goToExplore() {
      uni.switchTab({
        url: '/pages/index/index'
      });
    },
    
    // 格式化距离
    formatDistance(meters) {
      if (meters < 1000) {
        return `${Math.round(meters)}米`;
      } else {
        return `${(meters / 1000).toFixed(1)}公里`;
      }
    }
  }
}
</script>

<style lang="scss">
.container {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.nav-bar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 999;
  background: #4CAF50;
}

.status-bar {
  height: var(--status-bar-height);
}

.header {
  padding: 20rpx 30rpx;
  text-align: center;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 88rpx;
  
  .back-btn {
    position: absolute;
    left: 30rpx;
    color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 48rpx;
    height: 48rpx;

    .back-icon {
      width: 36rpx;
      height: 36rpx;
      position: relative;

      .back-arrow {
        width: 20rpx;
        height: 20rpx;
        border-left: 4rpx solid #fff;
        border-bottom: 4rpx solid #fff;
        transform: rotate(45deg);
        position: absolute;
        left: 8rpx;
        top: 8rpx;
      }
    }
  }
  
  .title {
    color: #fff;
    font-size: 36rpx;
    font-weight: 500;
  }
}

.page-content {
  padding-top: calc(var(--status-bar-height) + 88rpx);
  padding-bottom: 30rpx;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 60vh;
  
  .empty-icon {
    width: 160rpx;
    height: 160rpx;
    margin-bottom: 30rpx;
    opacity: 0.6;
  }
  
  .empty-text {
    font-size: 32rpx;
    color: #999;
    margin-bottom: 40rpx;
  }
  
  .explore-btn {
    background: #4CAF50;
    color: #fff;
    font-size: 28rpx;
    padding: 16rpx 60rpx;
    border-radius: 50rpx;
    border: none;
    
    &:after {
      border: none;
    }
  }
}

.location-list {
  padding: 20rpx;
}

.location-card {
  position: relative;
  background: #fff;
  border-radius: 16rpx;
  overflow: hidden;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  
  .location-image {
    width: 100%;
    height: 300rpx;
  }
  
  .location-info {
    padding: 20rpx;
  }
  
  .location-header {
    display: flex;
    align-items: center;
    margin-bottom: 10rpx;
    
    .location-name {
      font-size: 32rpx;
      font-weight: 500;
      color: #333;
      margin-right: 12rpx;
    }
    
    .type-tag {
      padding: 4rpx 12rpx;
      background: rgba(76, 175, 80, 0.1);
      color: #4CAF50;
      font-size: 24rpx;
      border-radius: 6rpx;
    }
  }
  
  .location-address {
    display: flex;
    align-items: center;
    margin: 10rpx 0;
    
    .icon-location {
      width: 24rpx;
      height: 24rpx;
      margin-right: 8rpx;
    }
    
    .address-text {
      font-size: 26rpx;
      color: #666;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      flex: 1;
    }
  }
  
  .location-footer {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 10rpx;
    
    .price {
      font-size: 28rpx;
      font-weight: 500;
      color: #FF6B00;
      
      &.free {
        color: #4CAF50;
      }
    }
    
    .distance {
      font-size: 24rpx;
      color: #999;
    }
  }
  
  .unfavorite-btn {
    position: absolute;
    top: 20rpx;
    right: 20rpx;
    width: 60rpx;
    height: 60rpx;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    
    .icon-heart {
      width: 36rpx;
      height: 36rpx;
    }
    
    &:active {
      background: rgba(255, 255, 255, 0.6);
    }
  }
}
</style> 