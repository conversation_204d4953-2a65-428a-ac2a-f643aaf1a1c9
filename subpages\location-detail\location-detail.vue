<template>
  <view class="container">
    <!-- 顶部导航栏 -->
    <view class="nav-bar">
      <view class="status-bar"></view>
      <view class="header">
        <view class="back-btn" @tap="goBack">
          <view class="back-icon">
            <view class="back-arrow"></view>
          </view>
        </view>
        <text class="title">场地详情</text>
      </view>
    </view>

    <!-- 页面内容区域 -->
    <view class="page-content">
      <!-- 图片轮播 -->
      <swiper class="image-swiper" circular :indicator-dots="true" :autoplay="true" interval="5000" duration="500">
        <swiper-item v-for="(image, index) in locationImages" :key="index">
          <image :src="image" mode="aspectFill" class="swiper-image" @tap="previewImage(locationImages,index)" />
        </swiper-item>
      </swiper>

      <!-- 场地基本信息 -->
      <view class="info-section">
        <view class="location-header">
          <view class="location-title">
            <text class="name">{{ locationData.name }}</text>
            <view class="type-tag">{{ locationData.typeName }}</view>
          </view>
          <view class="price-info">
            <text v-if="locationData.feeType == 'free'" class="price free">免费</text>
            <text v-else-if="locationData.feeType == 'unknown'" class="price free">未知</text>
            <text v-else-if="locationData.feeType == 'hourly'" class="price">¥{{ locationData.hourlyPrice }}/小时</text>
            <text v-else-if="locationData.feeType == 'perTime'" class="price">¥{{ locationData.perTimePrice }}/次</text>
          </view>
        </view>

        <!-- 距离信息 -->
        <view class="distance-info">
          <image src="/static/icons/map-pin.svg" class="icon-map"></image>
          <text class="distance">距离当前位置 {{ formatDistance(distance_meters) }}</text>
          <view class="refresh-btn" :class="{ 'refreshing': isRefreshing }" @tap="refreshDistance">
            <image src="/static/icons/refresh.svg" class="icon-refresh"></image>
          </view>
        </view>

        <!-- 地址信息 -->
        <view class="address-info" @tap="openMap">
          <image src="/static/icons/address.svg" class="icon-address"></image>
          <text class="address">{{ locationData.address }}</text>
          <image src="/static/icons/arrow.svg" class="icon-arrow"></image>
        </view>

        <!-- 手机号信息 -->
        <view class="phone-info"  v-if="locationData.phone && locationData.phone !== '未知'" style="display: flex;flex-direction: column;">
          <view class="phone-info" style="width: 100%;" @tap="callPhone(phone)" v-for="(phone,index) in locationData.phone.split(';')" :key="index">
            <image src="/static/icons/phone.svg" class="icon-phone"></image>
            <text class="phone" >{{ phone }}</text>
            <image src="/static/icons/arrow.svg" class="icon-arrow"></image>
          </view>
        </view>
        <view class="phone-info" v-else>
          <image src="/static/icons/phone.svg" class="icon-phone"></image>
          <text class="phone unknown">未知</text>
        </view>


        <!-- 描述信息 -->
        <view class="description-section">
          <view class="section-title">
            <image src="/static/icons/description.svg" class="icon-desc"></image>
            <text>场地描述</text>
          </view>
          <view class="description-content">
            {{ locationData.description || '暂无描述' }}
          </view>
        </view>
      </view>
      <view class="feedback" @tap="showFeedbackModal">
        <image src="/static/icons/edit.svg" class="icon-feedback"></image>
        <text>当前场地信息不对，我要修改</text>
      </view>
      
      <!-- 反馈弹窗 -->
      <uni-popup ref="feedbackPopup" type="center">
        <view class="feedback-modal">
          <view class="modal-header">
            <text class="modal-title">场地信息反馈</text>
            <view class="close-btn" @tap="closeFeedbackModal">×</view>
          </view>
          <view class="modal-body">
            <view class="form-item">
              <text class="label">错误类型</text>
              <view class="radio-group">
                <view class="radio-item" v-for="(item, index) in feedbackTypes" :key="index" @tap="selectFeedbackType(index)">
                  <view class="radio" :class="{'radio-selected': selectedFeedbackType === index}"></view>
                  <text>{{item}}</text>
                </view>
              </view>
            </view>
            <view class="form-item">
              <text class="label">详细说明</text>
              <textarea class="feedback-input" v-model="feedbackContent" placeholder="请详细描述需要修改的信息(限200字)" maxlength="200" />
            </view>
          </view>
          <view class="modal-footer">
            <button class="cancel-btn" @tap="closeFeedbackModal">取消</button>
            <button class="submit-btn" @tap="submitFeedback">提交</button>
          </view>
        </view>
      </uni-popup>
      <!-- 地图预览 -->
      <view class="map-section">
        <view class="section-title">
          <image src="/static/icons/location.svg" class="icon-location"></image>
          <text>场地位置</text>
        </view>
        <map class="location-map" :latitude="Number(locationData.latitude)" :longitude="Number(locationData.longitude)"
          :markers="mapMarkers" show-compass scale="17"></map>
      </view>

      <!-- 操作按钮 -->

      <view class="action-buttons" v-if="flag&&flag == 'examine'">
        <button class="action-btn navigate-btn" @tap="examine('approve')">
          <image src="/static/icons/navigate.svg" class="icon-navi"></image>
          <text>通过</text>
        </button>
       <button class="action-btn share-btn" @tap="examine('reject')">
          <image src="/static/icons/share.svg" class="icon-share"></image>
          <text>拒绝</text>
        </button>
      </view>


      <view class="action-buttons" v-else>
        <button class="action-btn navigate-btn" @tap="openMap">
          <image src="/static/icons/navigate.svg" class="icon-navi"></image>
          <text>导航</text>
        </button>
        <button class="action-btn favorite-btn" @tap="toggleFavorite" v-if="false">
          <image :src="isFavorite ? '/static/icons/heart-filled.svg' : '/static/icons/empty-heart.svg'"
            class="icon-heart"></image>
          <text>{{ isFavorite ? '已收藏' : '收藏' }}</text>
        </button>
        <button class="action-btn create-btn" @tap="createSportsEvent">
          <image src="/static/icons/sport.svg" class="icon-sport"></image>
          <text>发起约球</text>
        </button>
        <button class="action-btn share-btn" open-type="share">
          <image src="/static/icons/share.svg" class="icon-share"></image>
          <text>分享</text>
        </button>
      </view>

      
    </view>
  </view>
</template>

<script>
import {verifyLocation,feedbackLocation} from "@/Api/index.js"
import uniPopup from '../../uni_modules/uni-popup/components/uni-popup/uni-popup.vue'
export default {
  components: {
    uniPopup
  },
  data() {
    return {
      distance_meters: "",
      longitude: '',
      latitude: "",
      locationData: {
      },
      mapMarkers: [],
      isFavorite: false,
      isRefreshing: false,
      flag:"",
      feedbackContent: "",
      selectedFeedbackType: -1,
      feedbackTypes: ["地址错误", "联系方式错误", "场地名称错误", "场地类型错误", "其他信息错误"]
    }
  },

  computed: {
    locationImages() {
      try {
        if (typeof this.locationData.images === 'string') {
          return JSON.parse(this.locationData.images);
        } else if (Array.isArray(this.locationData.images)) {
          return this.locationData.images;
        }
        return [];
      } catch (e) {
        console.error('Failed to parse location images:', e);
        return [];
      }
    }
  },
  onLoad(options) {
    if (options.item) {
      let data = JSON.parse(decodeURIComponent(options.item));
      this.locationData = data;
      this.getCurrentLocation()
    }
    if(options.flag){
      this.flag = options.flag
    }

    // 初始化地图标记
    this.initMapMarkers();
    // 检查是否已收藏
    this.checkFavoriteStatus();
  },
  // 分享到微信好友
  onShareAppMessage() {

    return {
      title: this.locationData.name + ' - 运动场地',
      path: '/subpages/location-detail/location-detail?item=' + encodeURIComponent(JSON.stringify(this.locationData)),
      imageUrl: this.locationImages.length > 0 ? this.locationImages[0] : ''
    }
  },

  // 分享到朋友圈
  onShareTimeline() {
    return {
      title: this.locationData.name + ' - 运动场地',
      query: 'item=' + encodeURIComponent(JSON.stringify(this.locationData)),
      imageUrl: this.locationImages.length > 0 ? this.locationImages[0] : ''
    }
  },
  methods: {
    goBack() {
      
			// 查找上一页
			let pages = getCurrentPages();
			let beforePage = pages[pages.length - 2];
			// 判断上一页是否存在
      
			if (beforePage) {
				// 跳转回上一页
				uni.navigateBack({
					delta: 1
				});
			} else {
        uni.switchTab({
          url: '/pages/index/index'
        });

				// 如果上一页不存在，跳转到首页
        }
		},
    // 发起约球
    createSportsEvent() {
      if (!uni.getStorageSync('ydToken')) {
        uni.showModal({
          title: '提示',
          content: '请先登录后再发起约球',
          confirmText: '去登录',
          success: (res) => {
            if (res.confirm) {
              uni.switchTab({
                url: '/pages/profile/profile?flag=login'
              });
            }
          }
        });
        return;
      }
      
      // 准备场地信息
      const locationInfo = {
        id: this.locationData.id,
        name: this.locationData.name,
        address: this.locationData.address,
        latitude: this.locationData.latitude,
        longitude: this.locationData.longitude,
        images: this.locationData.images
      };
      
      // 跳转到发起约球页面并传递场地信息
      uni.navigateTo({
        url: '/subpages/create-sports-event/create-sports-event',
        success: (res) => {
          // 向目标页面传递数据
          res.eventChannel.emit('acceptLocationData', locationInfo);
        }
      });
    },
    async examine(flag){
      let {data} = await verifyLocation({id:this.locationData.id,status:flag})
      uni.showToast({ title: data.msg, icon: 'none' });
      setTimeout(() => {
        let pages = getCurrentPages();
        let beforePage = pages[pages.length - 2];
        uni.navigateBack({
								delta: 1,
								success: function () {
									// 更新上一页的数据
									if (beforePage && beforePage.$vm) {
										if (beforePage.$vm.getPendingLocationsData) {
											beforePage.$vm.getPendingLocationsData();
										}
									}
								}
							});

      }, 1000)
    },
  
    // 获取最新距离
    calculateDistance(lat1, lon1, lat2, lon2) {
      // 地球半径（单位：千米）
      const R = 6371;

      // 将角度转换为弧度
      const dLat = (lat2 - lat1) * Math.PI / 180;
      const dLon = (lon2 - lon1) * Math.PI / 180;

      // 计算Haversine值
      const a =
        Math.sin(dLat / 2) * Math.sin(dLat / 2) +
        Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
        Math.sin(dLon / 2) * Math.sin(dLon / 2);

      // 计算大圆距离
      const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

      // 计算距离（千米）
      const distanceInKm = R * c;

      // 转换为米
      const distanceInMeters = distanceInKm * 1000;

      return distanceInMeters;
    },
    // 刷新距离
    refreshDistance() {
      this.isRefreshing = true;
      this.getCurrentLocation();
      setTimeout(() => {
        this.isRefreshing = false;
      }, 800);
    },
    // 获取当前定位
    getCurrentLocation() {
      uni.getLocation({
        type: 'gcj02',
        success: (res) => {
          this.latitude = res.latitude
          this.longitude = res.longitude
          this.distance_meters = this.calculateDistance(res.latitude, res.longitude, this.locationData.latitude, this.locationData.longitude)
          console.log(this.distance_meters);

        },
        fail: () => {
          uni.showToast({
            title: '获取位置失败',
            icon: 'none'
          });
        }
      })
    },
    // 初始化地图标记
    initMapMarkers() {
      this.mapMarkers = [{
        id: 1,
        latitude: Number(this.locationData.latitude),
        longitude: Number(this.locationData.longitude),
        title: this.locationData.name,
        iconPath: '/static/icons/location-marker.svg', // 使用SVG标记图标
        width: 32,
        height: 32,
      }];
    },

    // 检查收藏状态
    checkFavoriteStatus() {
      try {
        let favorites = uni.getStorageSync('favorites') || [];
        if (Array.isArray(favorites)) {
          this.isFavorite = favorites.some(item => item.id === this.locationData.id);
        }
      } catch (e) {
        console.error('Failed to check favorite status:', e);
      }
    },

    // 切换收藏状态
    toggleFavorite() {
      if (!uni.getStorageSync('ydToken')) {
        uni.showModal({
          title: '提示',
          content: '请先登录后再收藏',
          confirmText: '去登录',
          success: (res) => {
            if (res.confirm) {
              uni.switchTab({
                url: '/pages/profile/profile?flag=login'
              });
            }
          }
        });
        return;
      }

      try {
        let favorites = uni.getStorageSync('favorites') || [];
        if (!Array.isArray(favorites)) {
          favorites = [];
        }

        if (this.isFavorite) {
          // 取消收藏
          favorites = favorites.filter(item => item.id !== this.locationData.id);
          uni.showToast({
            title: '已取消收藏',
            icon: 'none'
          });
        } else {
          // 添加收藏
          favorites.push(this.locationData);
          uni.showToast({
            title: '收藏成功',
            icon: 'success'
          });
        }

        uni.setStorageSync('favorites', favorites);
        this.isFavorite = !this.isFavorite;

        // 更新用户收藏数量
        let userInfo = this.$store.state.userinfo || {};
        userInfo.favorites = favorites.length;
        this.$store.state.userinfo = userInfo;
      } catch (e) {
        console.error('Failed to toggle favorite:', e);
        uni.showToast({
          title: '操作失败，请重试',
          icon: 'none'
        });
      }
    },

    // 格式化距离
    formatDistance(meters) {
      if (meters < 1000) {
        return `${Math.round(meters)}米`;
      } else {
        return `${(meters / 1000).toFixed(1)}公里`;
      }
    },

    // 打开地图导航
    openMap() {
      uni.openLocation({
        latitude: Number(this.locationData.latitude),
        longitude: Number(this.locationData.longitude),
        name: this.locationData.name,
        address: this.locationData.address,
        scale: 14
      });
    },

    // 分享场地
    shareLocation() {
      uni.showShareMenu({
        withShareTicket: true,
        menus: ['shareAppMessage', 'shareTimeline']
      });
    },

    // 呼叫电话
    callPhone(phone) {
      if (this.locationData.phone && this.locationData.phone !== '未知') {
        uni.makePhoneCall({
          phoneNumber: phone.toString()
        });
      }
    },
    
    // 预览图片
    previewImage(data,index) {
      uni.previewImage({
        current: data[index],
        urls: data
      });
    },
    
    // 显示反馈弹窗
    showFeedbackModal() {
      this.$refs.feedbackPopup.open();
    },
    
    // 关闭反馈弹窗
    closeFeedbackModal() {
      this.$refs.feedbackPopup.close();
      this.feedbackContent = "";
      this.selectedFeedbackType = -1;
    },
    
    // 选择反馈类型
    selectFeedbackType(index) {
      this.selectedFeedbackType = index;
    },
    
    submitFeedback() {
      if (this.selectedFeedbackType === -1) {
        uni.showToast({
          title: '请选择错误类型',
          icon: 'none'
        });
        return;
      }

      if (!this.feedbackContent.trim()) {
        uni.showToast({
          title: '请填写详细说明',
          icon: 'none'
        });
        return;
      }


      // 这里可以添加提交反馈的API调用
      uni.showLoading({
        title: '提交中...'
      });
      let obj = {
        sport_id: this.locationData.id,
        feedback_type: this.feedbackTypes[this.selectedFeedbackType],
        feedback_content: this.feedbackContent
      }
      feedbackLocation(obj).then(res => {
        this.closeFeedbackModal();
        uni.showToast({
          title: '反馈提交成功',
          icon: 'success'
        });
      }, err => {
        this.closeFeedbackModal();
        uni.showToast({
          title: '反馈提交失败',
          icon: 'error'
        });
      })



    },
  }
}
</script>

<style lang="scss">
.container {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.nav-bar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 999;
  background: #4CAF50;
}

.status-bar {
  height: var(--status-bar-height);
}

.header {
  padding: 20rpx 30rpx;
  text-align: center;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 88rpx;

  .back-btn {
    position: absolute;
    left: 30rpx;
    color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 48rpx;
    height: 48rpx;

    .back-icon {
      width: 36rpx;
      height: 36rpx;
      position: relative;

      .back-arrow {
        width: 20rpx;
        height: 20rpx;
        border-left: 4rpx solid #fff;
        border-bottom: 4rpx solid #fff;
        transform: rotate(45deg);
        position: absolute;
        left: 8rpx;
        top: 8rpx;
      }
    }
  }

  .title {
    color: #fff;
    font-size: 36rpx;
    font-weight: 500;
  }

  .back-btn {
    position: absolute;
    left: 30rpx;
    top: 50%;
    transform: translateY(-50%);
    width: 60rpx;
    height: 60rpx;
    display: flex;
    align-items: center;
    justify-content: center;

    .icon-back {
      color: #fff;
      font-size: 40rpx;
    }
  }
}

.page-content {
  padding-top: calc(var(--status-bar-height) + 88rpx);
}

.image-swiper {
  width: 100%;
  height: 500rpx;

  .swiper-image {
    width: 100%;
    height: 100%;
  }
}

.info-section {
  padding: 30rpx;
  background: #fff;
  margin-bottom: 20rpx;
}

.location-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20rpx;

  .location-title {
    flex: 1;

    .name {
      font-size: 36rpx;
      font-weight: bold;
      color: #333;
      margin-right: 16rpx;
    }

    .type-tag {
      display: inline-block;
      padding: 4rpx 16rpx;
      background: rgba(76, 175, 80, 0.1);
      color: #4CAF50;
      font-size: 24rpx;
      border-radius: 6rpx;
      margin-top: 10rpx;
    }
  }

  .price-info {
    .price {
      font-size: 32rpx;
      font-weight: bold;
      color: #FF6B00;

      &.free {
        color: #4CAF50;
      }
    }
  }
}

.distance-info,
.address-info {
  display: flex;
  align-items: center;
  padding: 16rpx 0;
  color: #666;
  font-size: 28rpx;

  .icon-map,
  .icon-address,
  .icon-arrow {
    width: 32rpx;
    height: 32rpx;
    margin-right: 16rpx;
    flex-shrink: 0;
  }

  .icon-arrow {
    margin-right: 0;
  }

  .address {
    flex: 1;
    line-height: 1.5;
  }
  
  .refresh-btn {
    width: 32rpx;
    height: 32rpx;
    margin-left: 12rpx;
    flex-shrink: 0;
    
    .icon-refresh {
      width: 100%;
      height: 100%;
    }
    
    &.refreshing {
      animation: rotate 0.8s linear infinite;
    }
  }
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.address-info {
  border-bottom: 1px solid #eee;
  padding-bottom: 20rpx;
}

.phone-info {
  display: flex;
  align-items: center;
  padding: 16rpx 0;
  color: #666;
  font-size: 28rpx;

  .icon-phone,
  .icon-arrow {
    width: 32rpx;
    height: 32rpx;
    margin-right: 16rpx;
    flex-shrink: 0;
  }

  .phone {
    flex: 1;
    line-height: 1.5;

    &.unknown {
      color: #999;
    }
  }
}

.description-section {
  margin-top: 20rpx;

  .section-title {
    display: flex;
    align-items: center;
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 16rpx;

    .icon-desc {
      width: 32rpx;
      height: 32rpx;
      margin-right: 10rpx;
    }
  }

  .description-content {
    font-size: 28rpx;
    color: #666;
    line-height: 1.5;
  }
}

.map-section {
  padding: 30rpx;
  background: #fff;
  margin-bottom: 100rpx;
  padding-bottom: 150rpx;
  .section-title {
    display: flex;
    align-items: center;
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 16rpx;

    .icon-location {
      width: 32rpx;
      height: 32rpx;
      margin-right: 10rpx;
    }
  }

  .location-map {
    width: 100%;
    height: 600rpx;
    border-radius: 16rpx;
  }
}

.feedback {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 20rpx 30rpx;
  padding: 20rpx;
  background: #fff;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  color: #4CAF50;
  font-size: 28rpx;
  
  .icon-feedback {
    width: 32rpx;
    height: 32rpx;
    margin-right: 10rpx;
  }
  
  &:active {
    background: #f9f9f9;
  }
}

.feedback-modal {
  width: 650rpx;
  background: #fff;
  border-radius: 16rpx;
  overflow: hidden;
  
  .modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 30rpx;
    border-bottom: 1px solid #eee;
    
    .modal-title {
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
    }
    
    .close-btn {
      width: 60rpx;
      height: 60rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 40rpx;
      color: #999;
    }
  }
  
  .modal-body {
    padding: 30rpx;
    
    .form-item {
      margin-bottom: 30rpx;
      
      .label {
        display: block;
        font-size: 28rpx;
        color: #333;
        margin-bottom: 16rpx;
      }
      
      .radio-group {
        display: flex;
        flex-wrap: wrap;
        
        .radio-item {
          display: flex;
          align-items: center;
          margin-right: 30rpx;
          margin-bottom: 20rpx;
          
          .radio {
            width: 36rpx;
            height: 36rpx;
            border-radius: 50%;
            border: 1px solid #ddd;
            margin-right: 10rpx;
            position: relative;
            
            &.radio-selected {
              border-color: #4CAF50;
              
              &:after {
                content: '';
                position: absolute;
                width: 20rpx;
                height: 20rpx;
                background: #4CAF50;
                border-radius: 50%;
                left: 50%;
                top: 50%;
                transform: translate(-50%, -50%);
              }
            }
          }
          
          text {
            font-size: 26rpx;
            color: #666;
          }
        }
      }
      
      .feedback-input {
        width: 100%;
        height: 200rpx;
        border: 1px solid #ddd;
        border-radius: 8rpx;
        padding: 20rpx;
        font-size: 28rpx;
        box-sizing: border-box;
      }
    }
  }
  
  .modal-footer {
    display: flex;
    border-top: 1px solid #eee;
    
    button {
      flex: 1;
      height: 90rpx;
      line-height: 90rpx;
      text-align: center;
      font-size: 30rpx;
      border-radius: 0;
      
      &:after {
        border: none;
      }
    }
    
    .cancel-btn {
      background: #f5f5f5;
      color: #666;
    }
    
    .submit-btn {
      background: #4CAF50;
      color: #fff;
    }
  }
}

.action-buttons {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  padding: 20rpx 30rpx;
  background: #fff;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.1);

  .action-btn {
    flex: 1;
    height: 80rpx;
    border-radius: 40rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 28rpx;
    margin: 0 10rpx;

    text {
      margin: 0 6rpx;
    }

    .icon-navi,
    .icon-share,
    .icon-heart,
    .icon-sport {
      width: 28rpx;
      height: 28rpx;
      margin-right: 6rpx;
    }
  }

  .action-btn:active {
    opacity: 0.8;
  }

  .navigate-btn {
    background: #4CAF50;
    color: #fff;
  }

  .favorite-btn {
    background: #fff;
    color: #FF6B00;
    border: 1px solid #FF6B00;

    &.active {
      background: rgba(255, 107, 0, 0.1);
    }
  }

  .share-btn {
    background: #fff;
    color: #4CAF50;
    border: 1px solid #4CAF50;
  }
  
  .create-btn {
    background: #FF9800;
    color: #fff;
  }
}
</style>