<template>
	<view class="container">
		<!-- 顶部导航栏 -->
		<view class="nav-bar">
			<view class="status-bar"></view>
			<view class="header">
				<view class="back-btn" @tap="goBack">
					<view class="back-icon">
						<view class="back-arrow"></view>
					</view>
				</view>
				<text class="title">发起约球</text>
			</view>
		</view>

		<!-- 页面内容区域 -->
		<view class="page-content">
			<form @submit="submitForm">
				<!-- 基本信息 -->
				<view class="form-card">
					<view class="form-item">
						<view class="form-label required">活动标题</view>
						<input class="form-input" type="text" v-model="formData.title" placeholder="请输入活动标题" maxlength="40" />
					</view>

					<view class="form-item">
						<view class="form-label required">运动类型</view>
						<picker :range="sportsOptions" range-key="sportsName" @change="onSportChange">
							<view class="form-picker">
								<text>{{ selectedSport ? selectedSport.sportsName : '请选择运动类型' }}</text>
								<text class="picker-arrow">▼</text>
							</view>
						</picker>
					</view>

					<view class="form-item">
						<view class="form-label required">活动日期（未来两周）</view>
						<view class="date-picker-container">
							<scroll-view scroll-x="true" class="date-scroll-view">
								<view 
									v-for="(date, index) in availableDates" 
									:key="index"
									:class="['date-item', formData.eventDate === date.value ? 'date-item-selected' : '']"
									@tap="selectDate(date.value)">
									<text class="date-day">{{ date.day }}</text>
									<text class="date-label">{{ date.label }}</text>
								</view>
							</scroll-view>
						</view>
					</view>

					<view class="form-item">
						<view class="form-label required">活动时间</view>
						<picker mode="time" :value="formData.eventTime" @change="onTimeChange">
							<view class="form-picker">
								<text>{{ formData.eventTime || '请选择时间' }}</text>
								<text class="picker-arrow">▼</text>
							</view>
						</picker>
					</view>

					<view class="form-item">
						<view class="form-label required">预计时长</view>
						<picker :range="durationOptions" @change="onDurationChange">
							<view class="form-picker">
								<text>{{ formData.duration }}小时</text>
								<text class="picker-arrow">▼</text>
							</view>
						</picker>
					</view>

					<view class="form-item">
						<view class="form-label required">参与人数上限</view>
						<picker :range="participantsOptions" @change="onParticipantsChange">
							<view class="form-picker">
								<text>{{ formData.maxParticipants }}人</text>
								<text class="picker-arrow">▼</text>
							</view>
						</picker>
					</view>

					<view class="form-item">
						<view class="form-label">人均费用</view>
						<view style="display: flex;align-items: center;">
							<view class="free-checkbox">
								<checkbox :checked="isFreeEvent" @tap="toggleFreeEvent" />
								<text>未知</text>
							</view>
							<input style="flex: 1;" class="form-input" type="digit" v-model="formData.fee" :placeholder="placeholder" :disabled="isFreeEvent"   maxlength="10"/>
							<text class="input-addon">元/人</text>
						</view>
					</view>
				</view>

				<!-- 地点信息 -->
				<view class="form-card">
					<view class="form-item">
						<view class="form-label required">活动场地</view>
						<view class="location-picker" @tap="selectLocation">
							<text v-if="formData.locationName">{{ formData.locationName }}</text>
							<text v-else class="placeholder-text">请选择活动场地</text>
							<text class="picker-arrow">▼</text>
						</view>
					</view>

					<view class="form-item" v-if="formData.locationName">
						<view class="form-label">详细地址</view>
						<view class="address-text">{{ formData.address }}</view>
					</view>

					<view class="form-item" v-if="formData.latitude && formData.longitude">
						<view class="map-preview">
							<map id="locationMap" class="map" :latitude="formData.latitude" :longitude="formData.longitude"
								:markers="markers" :scale="16"></map>
						</view>
					</view>
				</view>

				<!-- 活动说明 -->
				<view class="form-card">
					<view class="form-item">
						<view class="form-label">活动说明</view>
						<textarea class="form-textarea" v-model="formData.description" placeholder="请输入活动说明、要求、技术高低等（选填）"
							maxlength="200" />
						<view class="textarea-counter">{{ formData.description.length }}/200</view>
					</view>
				</view>

				<!-- 联系方式分享设置 -->
				<view class="form-card" v-if="false">
					<view class="form-item">
						<view class="form-label">联系方式设置</view>
						<view class="checkbox-container">
							<checkbox :checked="shareContact" @tap="toggleShareContact" />
							<text class="checkbox-label">允许参与者查看我的联系方式</text>
						</view>
						<view class="form-tip">勾选后，参与者可以看到您的手机号和微信号，便于活动联系</view>
					</view>
				</view>

				<!-- 提交按钮 -->
				<button class="submit-btn" form-type="submit" :disabled="submitting">{{ submitting ? '提交中...' : '发布活动'
					}}</button>
			</form>
		</view>

		<!-- 分享弹窗 -->
		<view v-if="showShareModal" class="share-modal-overlay" @tap="closeShareModal">
			<view class="share-modal" @tap.stop>
				<view class="share-header">
					<text class="share-title">活动发布成功！</text>
					<text class="share-subtitle">快分享给好友，邀请更多人参与吧！</text>
				</view>
				<view class="share-buttons">
					<button
						class="share-btn share-btn-primary"
						open-type="share"
						@tap="closeShareModal(1000)">
						立即分享
					</button>
					<button
						class="share-btn share-btn-secondary"
						@tap="closeShareModal">
						取消分享
					</button>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import { createSportsEvent,getAllSports } from "../../Api/index.js";

export default {
	data() {
		const today = this.formatDate(new Date());
		const twoWeeksLater = new Date();
		twoWeeksLater.setDate(twoWeeksLater.getDate() + 14);

		return {
			placeholder:"默认免费",
			today: today,
			maxDate: this.formatDate(twoWeeksLater),
			availableDates: this.generateAvailableDates(),
			isFreeEvent: false,
			formData: {
				title: '',
				sportType: '',
				eventDate: today,
				eventTime: '18:00',
				duration: 2,
				maxParticipants: 10,
				fee: '',
				locationId: '',
				locationName: '',
				address: '',
				latitude: '',
				longitude: '',
				description: ''
			},
			selectedSport: null,
			sportsOptions: [],
			durationOptions: ['1', '1.5', '2', '2.5', '3', '3.5', '4', '4.5', '5', '6'],
			participantsOptions: ['2', '4', '6', '8', '10', '12', '16', '20', '30'],
			markers: [],
			submitting: false,
			showShareModal: false,
			shareEventId: '',
			shareContact: true  // 默认勾选允许分享联系方式
		}
	},
	async 	onLoad() {
		this.availableDates = this.generateAvailableDates()
		let res = await getAllSports()
		this.sportsOptions=res.data.data.sports

		// 如果有选中的运动类型，默认选择第一个
		let data = uni.getStorageSync('selectedSports')
		if (data) {
			data = JSON.parse(data)
			if (data.length > 0) {
				this.selectedSport = data[0]
				this.formData.sportType = data[0].id
			}
		}

		// 如果没有选中的运动类型，且有运动类型列表，默认选择第一个
		if (!this.selectedSport && this.sportsOptions.length > 0) {
			this.selectedSport = this.sportsOptions[0]
			this.formData.sportType = this.sportsOptions[0].id
		}

		// 获取从场地详情页传递的场地信息
		const eventChannel = this.getOpenerEventChannel()
		eventChannel.on('acceptLocationData', (locationData) => {
			if (locationData) {
				// 自动填充场地信息
				this.formData.locationId = locationData.id
				this.formData.locationName = locationData.name
				this.formData.address = locationData.address
				this.formData.latitude = locationData.latitude
				this.formData.longitude = locationData.longitude
				this.formData.images = locationData.images

				// 设置地图标记
				this.markers = [{
					id: 1,
					latitude: locationData.latitude,
					longitude: locationData.longitude,
					title: locationData.name,
					iconPath: '/static/icons/location-marker.png',
					width: 32,
					height: 32
				}]
			}
		})
	},
	methods: {
		// 返回上一页
		goBack() {
			uni.navigateBack()
		},
		// 格式化日期
		formatDate(date) {
			const year = date.getFullYear()
			const month = (date.getMonth() + 1).toString().padStart(2, '0')
			const day = date.getDate().toString().padStart(2, '0')
			return `${year}-${month}-${day}`
		},
		// 生成可用日期列表
		generateAvailableDates() {
			const dates = [];
			const weekdays = ['日', '一', '二', '三', '四', '五', '六'];
			const today = new Date();
			
			// 生成今天到两周后的日期
			for (let i = 0; i < 15; i++) {
				const date = new Date();
				date.setDate(today.getDate() + i);
				
				const formattedDate = this.formatDate(date);
				const day = date.getDate();
				const weekday = weekdays[date.getDay()];
				
				let label;
				if (i === 0) {
					label = '今天';
				} else if (i === 1) {
					label = '明天';
				} else if (i === 2) {
					label = '后天';
				} else {
					label = `周${weekday}`;
				}
				
				dates.push({
					value: formattedDate,
					day: day,
					label: label
				});
			}
			
			return dates;
		},
		// 选择日期
		selectDate(date) {
			this.formData.eventDate = date;
		},
		// 运动类型变更
		onSportChange(e) {
			const index = e.detail.value
			this.selectedSport = this.sportsOptions[index]
			this.formData.sportType = this.selectedSport.id
		},
		// 日期变更
		onDateChange(e) {
			this.formData.eventDate = e.detail.value
		},
		// 时间变更
		onTimeChange(e) {
			this.formData.eventTime = e.detail.value
		},
		// 时长变更
		onDurationChange(e) {
			this.formData.duration = this.durationOptions[e.detail.value]
		},
		// 人数变更
		onParticipantsChange(e) {
			this.formData.maxParticipants = this.participantsOptions[e.detail.value]
		},
		// 切换免费状态
		toggleFreeEvent() {
			this.isFreeEvent = !this.isFreeEvent
			if (this.isFreeEvent) {
				this.placeholder = '未知费用'
			} else {
				this.placeholder = '默认免费'
			}
		},
		// 切换联系方式分享状态
		toggleShareContact() {
			this.shareContact = !this.shareContact
		},
		// 选择活动场地
		selectLocation() {
			uni.navigateTo({
				url: '/subpages/location-select/location-select?type=' + this.formData.sportType,
				events: {
					// 监听选择场地页面返回的数据
					selectLocation: (location) => {
						this.formData.locationId = location.id
						this.formData.locationName = location.name
						this.formData.address = location.address
						this.formData.latitude = location.latitude
						this.formData.longitude = location.longitude
						this.formData.images = location.images
						// 设置地图标记
						this.markers = [{
							id: 1,
							latitude: location.latitude,
							longitude: location.longitude,
							title: location.name,
							iconPath: '/static/location-marker.png',
							width: 32,
							height: 32
						}]
					}
				}
			})
		},
		// 提交表单
		submitForm() {

			// 表单验证
			if (!this.formData.title.trim()) {
				uni.showToast({
					title: '请输入活动标题',
					icon: 'none'
				})
				return
			}

			if (!this.formData.sportType) {
				uni.showToast({
					title: '请选择运动类型',
					icon: 'none'
				})
				return
			}

			if (!this.formData.eventDate || !this.formData.eventTime) {
				uni.showToast({
					title: '请选择活动时间',
					icon: 'none'
				})
				return
			}

			if (!this.formData.locationId) {
				uni.showToast({
					title: '请选择活动场地',
					icon: 'none'
				})
				return
			}

			// 检查用户是否勾选了分享联系方式但没有填写手机号或微信号
			const userInfo = this.$store.state.userinfo
			if (this.shareContact && (!userInfo.phone && !userInfo.wechat)) {
				uni.showModal({
					title: '提示',
					content: '您未设置手机号或微信号，请前往我的页面设置',
					confirmText: '去设置',
					success: (res) => {
						if (res.confirm) {
							uni.reLaunch({
								url: '/pages/profile/profile?showPopup=true'
							})
						}
					}
				})
				return
			}

			uni.requestSubscribeMessage({
				tmplIds: ['o-8tpZjbcOPkrrw54S7SXX9Erk3qlQ8997NI6Yj2BAY'],
				success: (res) => {

					let sub_num = -1
					let str = res['o-8tpZjbcOPkrrw54S7SXX9Erk3qlQ8997NI6Yj2BAY']
					if (str == 'accept') {
						sub_num=1
					}else{
						sub_num=0
					}
					// 组装时间
					const eventDateTime = `${this.formData.eventDate} ${this.formData.eventTime}:00`
						const eventTimestamp = new Date(eventDateTime).getTime()

						// 检查时间是否有效
						const oneHourFromNow = Date.now() + 60 * 60 * 1000; // 当前时间加一小时
						if (isNaN(eventTimestamp) || eventTimestamp <= oneHourFromNow) {
							uni.showToast({
								title: '活动时间必须至少在当前时间一小时后',
								icon: 'none'
							})
							return
						}

						// 防止重复提交
						if (this.submitting) return
						this.submitting = true

						// 提交数据
						const submitData = {
							title: this.formData.title,
							sportType: this.formData.sportType,
							eventTime: eventTimestamp,
							duration: this.formData.duration,
							maxParticipants: this.formData.maxParticipants,
							fee: this.isFreeEvent ?"未知":this.formData.fee.trim()==''?'免费':this.formData.fee.trim(),
							locationId: this.formData.locationId,
							description: this.formData.description,
							location_name: this.formData.locationName,
							address: this.formData.address,
							latitude: this.formData.latitude,
							longitude: this.formData.longitude,
							images: this.formData.images,
							sub_num: sub_num,
							show_contact: this.shareContact ? 1 : 0  // 添加show_contact参数
						}

						// 如果用户勾选了分享联系方式，则传递phone和wechat
						if (this.shareContact) {
							submitData.phone = userInfo.phone || ''
							submitData.wechat = userInfo.wechat || ''
						} else {
							submitData.phone = ''
							submitData.wechat = ''
						}
						

						createSportsEvent(submitData).then(res => {
							this.submitting = false

							if (res.data.code === 200) {
								uni.showToast({
									title: '发布成功',
									icon: 'success'
								})

								// 显示分享提醒弹窗
								setTimeout(() => {
									this.showShareReminder(res.data.data.eventId)
								}, 1500)
							} else {
								uni.showToast({
									title: res.data.message || '发布失败',
									icon: 'none'
								})
							}
						}).catch(() => {
							this.submitting = false
							uni.showToast({
								title: '网络错误，请稍后重试',
								icon: 'none'
							})
						})
				}
			})


		},

		// 显示分享提醒
		showShareReminder(eventId) {
			// 显示分享弹窗，使用原生button实现分享
			this.shareEventId = eventId;
			this.showShareModal = true;
		},

		// 处理分享按钮点击
		onShareAppMessage() {
			return {
				title: '我发布了一个约球活动，快来参加吧！',
				path: `/subpages/sports-event-detail/sports-event-detail?id=${this.shareEventId}&sportType=${this.formData.sportType}`,
				imageUrl: this.formData.images[0]
			}
		},

		// 关闭分享弹窗
		closeShareModal(time) {
			if(time){
				setTimeout(() => {
					this.showShareModal = false;
					uni.redirectTo({
						 url: `/subpages/sports-event-detail/sports-event-detail?id=${this.shareEventId}&sportType=${this.formData.sportType}`,
					});
				}, time);
			}

		}
	}
}
</script>

<style lang="scss" scoped>
view{
	box-sizing: border-box!important;
}
.container {
	display: flex;
	flex-direction: column;
	background-color: #f5f5f5;
}

.nav-bar {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	z-index: 999;
	background: #4CAF50;
}

.header {
	padding: 20rpx 30rpx;
	display: flex;
	align-items: center;
	position: relative;

	.back-btn {
		position: absolute;
		left: 30rpx;
		color: #fff;
		display: flex;
		align-items: center;
		justify-content: center;
		width: 48rpx;
		height: 48rpx;

		.back-icon {
			width: 36rpx;
			height: 36rpx;
			position: relative;

			.back-arrow {
				width: 20rpx;
				height: 20rpx;
				border-left: 4rpx solid #fff;
				border-bottom: 4rpx solid #fff;
				transform: rotate(45deg);
				position: absolute;
				left: 8rpx;
				top: 8rpx;
			}
		}
	}

	.title {
		flex: 1;
		text-align: center;
		color: #fff;
		font-size: 36rpx;
		font-weight: 500;
	}
}

.page-content {
	flex: 1;
	margin-top: calc(var(--status-bar-height) + 88rpx);
	padding: 30rpx;
	padding-bottom: 60rpx;
}

.form-card {
	background-color: #ffffff;
	border-radius: 20rpx;
	padding: 30rpx;
	margin-bottom: 30rpx;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
	overflow: hidden;

}

.form-item {
	margin-bottom: 30rpx;
}

.form-item:last-child {
	margin-bottom: 0;
}

.form-label {
	font-size: 28rpx;
	color: #333333;
	margin-bottom: 16rpx;
}

.required::after {
	content: '*';
	color: #f44336;
	margin-left: 8rpx;
}

.form-input {
	width: 100%;
	height: 88rpx;
	background-color: #f5f5f5;
	border-radius: 16rpx;
	padding: 0 24rpx;
	font-size: 28rpx;
	color: #333333;
	box-sizing: border-box;
}

.form-picker {
	width: 100%;
	height: 88rpx;
	background-color: #f5f5f5;
	border-radius: 16rpx;
	padding: 0 24rpx;
	font-size: 28rpx;
	color: #333333;
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.date-picker-container {
	width: 100%;
	overflow: hidden;
}

.date-scroll-view {
	white-space: nowrap;
	width: 100%;
}

.date-item {
	display: inline-flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	width: 110rpx;
	height: 110rpx;
	margin-right: 16rpx;
	border-radius: 16rpx;
	background-color: #f5f5f5;
}

.date-item-selected {
	background-color: #4CAF50;
}

.date-item-selected .date-day,
.date-item-selected .date-label {
	color: #ffffff;
}

.date-day {
	font-size: 32rpx;
	font-weight: bold;
	color: #333333;
}

.date-label {
	font-size: 24rpx;
	color: #666666;
	margin-top: 4rpx;
}

.picker-arrow {
	color: #999999;
	font-size: 24rpx;
}

.input-addon {
	font-size: 28rpx;
	color: #999999;
	width: fit-content;
	flex-shrink: 0;
	margin-left: 6rpx;
}

.free-checkbox {
	display: flex;
	align-items: center;
	margin-right: 10rpx;
	
	checkbox {
		margin-right: 4rpx;
		transform: scale(0.8);
	}
	
	text {
		width: fit-content;
		flex-shrink: 0;
		font-size: 28rpx;
		color: #333;
	}
}

.location-picker {
	width: 100%;
	height: 88rpx;
	background-color: #f5f5f5;
	border-radius: 16rpx;
	padding: 0 24rpx;
	font-size: 28rpx;
	color: #333333;
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.placeholder-text {
	color: #999999;
}

.address-text {
	font-size: 28rpx;
	color: #666666;
	line-height: 1.4;
}

.map-preview {
	height: 300rpx;
	border-radius: 16rpx;
	overflow: hidden;
	margin-top: 20rpx;
}

.map {
	width: 100%;
	height: 100%;
}

.form-textarea {
	width: 100%;
	height: 240rpx;
	background-color: #f5f5f5;
	border-radius: 16rpx;
	padding: 24rpx;
	font-size: 28rpx;
	color: #333333;
	box-sizing: border-box;
}

.textarea-counter {
	text-align: right;
	font-size: 24rpx;
	color: #999999;
	margin-top: 10rpx;
}

.checkbox-container {
	display: flex;
	align-items: center;
	margin-top: 20rpx;
}

.checkbox-label {
	font-size: 28rpx;
	color: #333;
	margin-left: 16rpx;
}

.form-tip {
	font-size: 24rpx;
	color: #999;
	margin-top: 16rpx;
	line-height: 1.4;
}

.submit-btn {
	background-color: #4CAF50;
	color: #ffffff;
	height: 100rpx;
	line-height: 100rpx;
	font-size: 32rpx;
	border-radius: 50rpx;
	margin-top: 40rpx;
}

.submit-btn[disabled] {
	background-color: #cccccc;
	color: #ffffff;
}

/* 分享弹窗样式 */
.share-modal-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-color: rgba(0, 0, 0, 0.5);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 9999;
}

.share-modal {
	background-color: #ffffff;
	border-radius: 20rpx;
	padding: 60rpx 40rpx 40rpx;
	margin: 0 40rpx;
	max-width: 600rpx;
	width: 100%;
}

.share-header {
	text-align: center;
	margin-bottom: 60rpx;
}

.share-title {
	display: block;
	font-size: 36rpx;
	font-weight: bold;
	color: #333333;
	margin-bottom: 20rpx;
}

.share-subtitle {
	display: block;
	font-size: 28rpx;
	color: #666666;
	line-height: 1.5;
}

.share-buttons {
	display: flex;
	gap: 20rpx;
	flex-direction: row-reverse;
	
}

.share-btn {
	height: 88rpx;
	line-height: 88rpx;
	font-size: 32rpx;
	border-radius: 44rpx;
	border: none;
	text-align: center;
}

.share-btn-primary {
	background-color: #4CAF50;
	color: #ffffff;
}

.share-btn-secondary {
	background-color: #f5f5f5;
	color: #666666;
}
</style>